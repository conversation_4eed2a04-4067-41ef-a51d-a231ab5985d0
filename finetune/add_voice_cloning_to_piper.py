#!/usr/bin/env python3
"""
Add voice cloning capabilities to existing Piper model.
This script modifies your trained Piper model to support voice cloning.
"""

import os
import sys
import json
import torch
import logging
from pathlib import Path
from typing import Dict, Any, Optional

# Add piper to path
piper_path = Path("piper/src/python")
if piper_path.exists():
    sys.path.insert(0, str(piper_path))

try:
    from piper_train.vits.lightning import VitsModel
    from piper_train.vits.models import SynthesizerTrn
except ImportError as e:
    print(f"❌ Failed to import piper_train: {e}")
    print("Make sure you're in the virtual environment with piper_train installed.")
    sys.exit(1)

from piper_voice_cloning.vits_voice_cloning import VITSVoiceCloning
from piper_voice_cloning.speaker_encoder import PiperSpeakerEncoder

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VoiceCloningUpgrader:
    """Upgrade existing Piper model with voice cloning capabilities."""
    
    def __init__(self, checkpoint_path: str, config_path: str):
        self.checkpoint_path = Path(checkpoint_path)
        self.config_path = Path(config_path)
        
        if not self.checkpoint_path.exists():
            raise FileNotFoundError(f"Checkpoint not found: {checkpoint_path}")
        if not self.config_path.exists():
            raise FileNotFoundError(f"Config not found: {config_path}")
        
        # Load config
        with open(self.config_path, 'r') as f:
            self.config = json.load(f)
        
        logger.info(f"Loaded config from {self.config_path}")
    
    def load_original_model(self) -> VitsModel:
        """Load the original Piper model."""
        logger.info(f"Loading original model from {self.checkpoint_path}")
        
        # Load checkpoint
        model = VitsModel.load_from_checkpoint(
            str(self.checkpoint_path),
            map_location="cpu"
        )
        
        logger.info("Original model loaded successfully")
        return model
    
    def create_voice_cloning_model(
        self,
        original_model: VitsModel,
        speaker_embedding_dim: int = 256
    ) -> VITSVoiceCloning:
        """Create voice cloning model from original model."""
        logger.info("Creating voice cloning model...")
        
        # Extract VITS config from original model
        vits_config = {
            'n_vocab': getattr(original_model.model_g, 'n_vocab', 256),
            'spec_channels': getattr(original_model.model_g, 'spec_channels', 513),
            'segment_size': getattr(original_model.model_g, 'segment_size', 32),
            'inter_channels': getattr(original_model.model_g, 'inter_channels', 192),
            'hidden_channels': getattr(original_model.model_g, 'hidden_channels', 192),
            'filter_channels': getattr(original_model.model_g, 'filter_channels', 768),
            'n_heads': getattr(original_model.model_g, 'n_heads', 2),
            'n_layers': getattr(original_model.model_g, 'n_layers', 6),
            'kernel_size': getattr(original_model.model_g, 'kernel_size', 3),
            'p_dropout': getattr(original_model.model_g, 'p_dropout', 0.1),
            'resblock': getattr(original_model.model_g, 'resblock', '1'),
            'resblock_kernel_sizes': getattr(original_model.model_g, 'resblock_kernel_sizes', [3, 7, 11]),
            'resblock_dilation_sizes': getattr(original_model.model_g, 'resblock_dilation_sizes', [[1, 3, 5], [1, 3, 5], [1, 3, 5]]),
            'upsample_rates': getattr(original_model.model_g, 'upsample_rates', [8, 8, 2, 2]),
            'upsample_initial_channel': getattr(original_model.model_g, 'upsample_initial_channel', 512),
            'upsample_kernel_sizes': getattr(original_model.model_g, 'upsample_kernel_sizes', [16, 16, 4, 4]),
            'n_speakers': getattr(original_model.model_g, 'n_speakers', 0),
            'gin_channels': getattr(original_model.model_g, 'gin_channels', 0),
        }
        
        # Create voice cloning model
        voice_cloning_model = VITSVoiceCloning(
            vits_config=vits_config,
            speaker_embedding_dim=speaker_embedding_dim,
            enable_voice_cloning=True
        )
        
        # Transfer weights from original model
        self._transfer_weights(original_model, voice_cloning_model)
        
        logger.info("Voice cloning model created successfully")
        return voice_cloning_model
    
    def _transfer_weights(
        self,
        original_model: VitsModel,
        voice_cloning_model: VITSVoiceCloning
    ):
        """Transfer weights from original model to voice cloning model."""
        logger.info("Transferring weights...")
        
        # Get state dicts
        original_state = original_model.model_g.state_dict()
        
        # Transfer compatible weights
        voice_cloning_state = voice_cloning_model.vits.state_dict()
        
        transferred_keys = []
        for key in original_state.keys():
            if key in voice_cloning_state:
                if original_state[key].shape == voice_cloning_state[key].shape:
                    voice_cloning_state[key] = original_state[key]
                    transferred_keys.append(key)
                else:
                    logger.warning(f"Shape mismatch for {key}: {original_state[key].shape} vs {voice_cloning_state[key].shape}")
        
        # Load the transferred weights
        voice_cloning_model.vits.load_state_dict(voice_cloning_state)
        
        logger.info(f"Transferred {len(transferred_keys)} weight tensors")
    
    def save_voice_cloning_model(
        self,
        voice_cloning_model: VITSVoiceCloning,
        output_dir: str,
        model_name: str = "voice_cloning_model"
    ):
        """Save the voice cloning model."""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Save model
        model_path = output_path / f"{model_name}.pth"
        torch.save({
            'model_state_dict': voice_cloning_model.state_dict(),
            'config': self.config,
            'voice_cloning_enabled': True,
            'speaker_embedding_dim': voice_cloning_model.speaker_embedding_dim
        }, model_path)
        
        # Save config
        config_path = output_path / f"{model_name}_config.json"
        updated_config = self.config.copy()
        updated_config['voice_cloning_enabled'] = True
        updated_config['speaker_embedding_dim'] = voice_cloning_model.speaker_embedding_dim
        
        with open(config_path, 'w') as f:
            json.dump(updated_config, f, indent=2)
        
        logger.info(f"Voice cloning model saved to {model_path}")
        logger.info(f"Config saved to {config_path}")
        
        return model_path, config_path

def find_latest_checkpoint() -> Optional[Path]:
    """Find the latest checkpoint."""
    lightning_logs_dir = Path("training_output/lightning_logs")
    
    if not lightning_logs_dir.exists():
        return None
    
    version_dirs = [d for d in lightning_logs_dir.iterdir() if d.is_dir() and d.name.startswith("version_")]
    if not version_dirs:
        return None
    
    latest_version = max(version_dirs, key=lambda x: int(x.name.split("_")[1]))
    checkpoint_dir = latest_version / "checkpoints"
    
    if not checkpoint_dir.exists():
        return None
    
    checkpoints = list(checkpoint_dir.glob("*.ckpt"))
    if not checkpoints:
        return None
    
    return max(checkpoints, key=lambda x: x.stat().st_mtime)

def main():
    """Main function to upgrade Piper model with voice cloning."""
    print("🎭 Adding Voice Cloning to Piper Model")
    print("=" * 50)
    
    # Find latest checkpoint
    checkpoint_path = find_latest_checkpoint()
    if not checkpoint_path:
        print("❌ No checkpoint found!")
        print("Make sure you have trained a Piper model first.")
        return
    
    config_path = Path("training_output/config.json")
    if not config_path.exists():
        print("❌ Config file not found!")
        return
    
    print(f"📁 Using checkpoint: {checkpoint_path}")
    print(f"📁 Using config: {config_path}")
    
    try:
        # Create upgrader
        upgrader = VoiceCloningUpgrader(
            checkpoint_path=str(checkpoint_path),
            config_path=str(config_path)
        )
        
        # Load original model
        original_model = upgrader.load_original_model()
        
        # Create voice cloning model
        voice_cloning_model = upgrader.create_voice_cloning_model(original_model)
        
        # Save voice cloning model
        output_dir = "training_output/voice_cloning_models"
        model_path, config_path = upgrader.save_voice_cloning_model(
            voice_cloning_model,
            output_dir,
            "piper_voice_cloning"
        )
        
        print("\n🎉 Voice cloning upgrade completed!")
        print(f"📁 Model saved to: {model_path}")
        print(f"📁 Config saved to: {config_path}")
        
        print("\n🚀 Next steps:")
        print("1. Test voice cloning with: python test_piper_voice_cloning.py")
        print("2. Convert to ONNX with voice cloning support")
        print("3. Integrate with your piper_api")
        
    except Exception as e:
        logger.error(f"Failed to upgrade model: {e}")
        print(f"❌ Upgrade failed: {e}")

if __name__ == "__main__":
    main()
