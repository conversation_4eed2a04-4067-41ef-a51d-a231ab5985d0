#!/usr/bin/env python3
"""
Speaker Encoder for Piper VITS Voice Cloning.
This module adds speaker embedding extraction capabilities to <PERSON>.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchaudio
import numpy as np
from typing import Optional, Tuple, Union
import logging

logger = logging.getLogger(__name__)

class ResNetBlock(nn.Module):
    """ResNet block for speaker encoder."""
    
    def __init__(self, in_channels: int, out_channels: int, stride: int = 1):
        super().__init__()
        self.conv1 = nn.Conv1d(in_channels, out_channels, 3, stride=stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm1d(out_channels)
        self.conv2 = nn.Conv1d(out_channels, out_channels, 3, padding=1, bias=False)
        self.bn2 = nn.BatchNorm1d(out_channels)
        
        self.shortcut = nn.Sequential()
        if stride != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv1d(in_channels, out_channels, 1, stride=stride, bias=False),
                nn.BatchNorm1d(out_channels)
            )
    
    def forward(self, x):
        out = F.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out += self.shortcut(x)
        out = F.relu(out)
        return out

class SpeakerEncoder(nn.Module):
    """
    Speaker encoder for extracting speaker embeddings from audio.
    Based on ResNet architecture optimized for speaker recognition.
    """
    
    def __init__(
        self,
        input_dim: int = 80,  # Mel spectrogram features
        embedding_dim: int = 256,  # Speaker embedding dimension
        hidden_dim: int = 512,
        num_layers: int = 4
    ):
        super().__init__()
        
        self.input_dim = input_dim
        self.embedding_dim = embedding_dim
        self.hidden_dim = hidden_dim
        
        # Input projection
        self.input_conv = nn.Conv1d(input_dim, 64, 3, padding=1)
        self.input_bn = nn.BatchNorm1d(64)
        
        # ResNet layers
        self.layers = nn.ModuleList()
        in_channels = 64
        
        for i in range(num_layers):
            out_channels = min(hidden_dim, 64 * (2 ** i))
            stride = 2 if i > 0 else 1
            self.layers.append(ResNetBlock(in_channels, out_channels, stride))
            in_channels = out_channels
        
        # Global average pooling and final projection
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        self.fc = nn.Linear(in_channels, embedding_dim)
        self.dropout = nn.Dropout(0.1)
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        """Initialize weights."""
        if isinstance(module, (nn.Conv1d, nn.Linear)):
            nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
            if module.bias is not None:
                nn.init.constant_(module.bias, 0)
        elif isinstance(module, nn.BatchNorm1d):
            nn.init.constant_(module.weight, 1)
            nn.init.constant_(module.bias, 0)
    
    def forward(self, mel_spectrogram: torch.Tensor) -> torch.Tensor:
        """
        Extract speaker embedding from mel spectrogram.
        
        Args:
            mel_spectrogram: [batch_size, mel_bins, time_steps]
        
        Returns:
            speaker_embedding: [batch_size, embedding_dim]
        """
        # Input projection
        x = F.relu(self.input_bn(self.input_conv(mel_spectrogram)))
        
        # ResNet layers
        for layer in self.layers:
            x = layer(x)
        
        # Global pooling
        x = self.global_pool(x)  # [batch_size, channels, 1]
        x = x.squeeze(-1)  # [batch_size, channels]
        
        # Final projection
        x = self.dropout(x)
        embedding = self.fc(x)  # [batch_size, embedding_dim]
        
        # L2 normalize
        embedding = F.normalize(embedding, p=2, dim=1)
        
        return embedding

class AudioPreprocessor:
    """Preprocess audio for speaker encoder."""
    
    def __init__(
        self,
        sample_rate: int = 22050,
        n_mels: int = 80,
        n_fft: int = 1024,
        hop_length: int = 256,
        win_length: int = 1024,
        f_min: float = 0.0,
        f_max: Optional[float] = None
    ):
        self.sample_rate = sample_rate
        self.n_mels = n_mels
        self.n_fft = n_fft
        self.hop_length = hop_length
        self.win_length = win_length
        self.f_min = f_min
        self.f_max = f_max or sample_rate // 2
        
        # Create mel spectrogram transform
        self.mel_transform = torchaudio.transforms.MelSpectrogram(
            sample_rate=sample_rate,
            n_fft=n_fft,
            hop_length=hop_length,
            win_length=win_length,
            n_mels=n_mels,
            f_min=f_min,
            f_max=self.f_max,
            power=2.0,
            normalized=False
        )
    
    def __call__(self, audio: torch.Tensor) -> torch.Tensor:
        """
        Convert audio to mel spectrogram.
        
        Args:
            audio: [batch_size, time] or [time]
        
        Returns:
            mel_spec: [batch_size, n_mels, time_frames]
        """
        if audio.dim() == 1:
            audio = audio.unsqueeze(0)
        
        # Compute mel spectrogram
        mel_spec = self.mel_transform(audio)
        
        # Convert to log scale
        mel_spec = torch.log(torch.clamp(mel_spec, min=1e-5))
        
        return mel_spec

class PiperSpeakerEncoder:
    """
    Complete speaker encoder system for Piper voice cloning.
    Handles audio preprocessing and speaker embedding extraction.
    """
    
    def __init__(
        self,
        model_path: Optional[str] = None,
        device: str = "auto",
        embedding_dim: int = 256
    ):
        self.device = device if device != "auto" else ("cuda" if torch.cuda.is_available() else "cpu")
        self.embedding_dim = embedding_dim
        
        # Initialize components
        self.preprocessor = AudioPreprocessor()
        self.encoder = SpeakerEncoder(embedding_dim=embedding_dim)
        
        # Move to device
        self.encoder.to(self.device)
        
        # Load pretrained weights if available
        if model_path and torch.cuda.is_available():
            try:
                self.load_model(model_path)
                logger.info(f"Loaded speaker encoder from {model_path}")
            except Exception as e:
                logger.warning(f"Failed to load speaker encoder: {e}")
                logger.info("Using randomly initialized speaker encoder")
    
    def load_model(self, model_path: str):
        """Load pretrained speaker encoder."""
        checkpoint = torch.load(model_path, map_location=self.device)
        if 'speaker_encoder' in checkpoint:
            self.encoder.load_state_dict(checkpoint['speaker_encoder'])
        else:
            self.encoder.load_state_dict(checkpoint)
    
    def save_model(self, model_path: str):
        """Save speaker encoder."""
        torch.save(self.encoder.state_dict(), model_path)
    
    def extract_embedding(
        self,
        audio: Union[torch.Tensor, np.ndarray],
        sample_rate: Optional[int] = None
    ) -> torch.Tensor:
        """
        Extract speaker embedding from audio.
        
        Args:
            audio: Audio waveform
            sample_rate: Sample rate of audio
        
        Returns:
            embedding: Speaker embedding [embedding_dim]
        """
        # Convert to tensor
        if isinstance(audio, np.ndarray):
            audio = torch.from_numpy(audio).float()
        
        # Resample if needed
        if sample_rate and sample_rate != self.preprocessor.sample_rate:
            resampler = torchaudio.transforms.Resample(
                orig_freq=sample_rate,
                new_freq=self.preprocessor.sample_rate
            )
            audio = resampler(audio)
        
        # Move to device
        audio = audio.to(self.device)
        
        # Preprocess
        mel_spec = self.preprocessor(audio)
        
        # Extract embedding
        with torch.no_grad():
            self.encoder.eval()
            embedding = self.encoder(mel_spec)
            
            if embedding.dim() > 1:
                embedding = embedding.squeeze(0)
        
        return embedding
    
    def compute_similarity(
        self,
        embedding1: torch.Tensor,
        embedding2: torch.Tensor
    ) -> float:
        """Compute cosine similarity between embeddings."""
        similarity = F.cosine_similarity(
            embedding1.unsqueeze(0),
            embedding2.unsqueeze(0)
        )
        return similarity.item()
    
    def train_mode(self):
        """Set encoder to training mode."""
        self.encoder.train()
    
    def eval_mode(self):
        """Set encoder to evaluation mode."""
        self.encoder.eval()

def create_speaker_encoder(
    embedding_dim: int = 256,
    device: str = "auto"
) -> PiperSpeakerEncoder:
    """Create a speaker encoder for Piper voice cloning."""
    return PiperSpeakerEncoder(
        embedding_dim=embedding_dim,
        device=device
    )
