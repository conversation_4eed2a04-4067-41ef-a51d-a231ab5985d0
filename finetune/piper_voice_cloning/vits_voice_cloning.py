#!/usr/bin/env python3
"""
Modified VITS model for Piper with voice cloning capabilities.
Extends the existing Piper VITS architecture to support speaker embeddings.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Dict, Any, Tuple
import logging

try:
    from piper_train.vits.models import SynthesizerTrn
    from piper_train.vits.commons import sequence_mask
except ImportError:
    # Fallback for development
    logging.warning("Piper train modules not found. Using mock classes.")
    
    class SynthesizerTrn(nn.Module):
        def __init__(self, *args, **kwargs):
            super().__init__()
            self.n_speakers = kwargs.get('n_speakers', 1)
        
        def forward(self, *args, **kwargs):
            return torch.randn(1, 80, 100), torch.randn(1, 1, 2200)

from .speaker_encoder import PiperSpeakerEncoder

logger = logging.getLogger(__name__)

class SpeakerProjection(nn.Module):
    """Project speaker embeddings to VITS speaker space."""
    
    def __init__(
        self,
        speaker_embedding_dim: int = 256,
        vits_speaker_dim: int = 192,
        hidden_dim: int = 512
    ):
        super().__init__()
        
        self.projection = nn.Sequential(
            nn.Linear(speaker_embedding_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, vits_speaker_dim)
        )
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                nn.init.constant_(module.bias, 0)
    
    def forward(self, speaker_embedding: torch.Tensor) -> torch.Tensor:
        """
        Project speaker embedding to VITS speaker space.
        
        Args:
            speaker_embedding: [batch_size, speaker_embedding_dim]
        
        Returns:
            vits_speaker_emb: [batch_size, vits_speaker_dim]
        """
        return self.projection(speaker_embedding)

class VITSVoiceCloning(nn.Module):
    """
    VITS model extended with voice cloning capabilities.
    Integrates speaker encoder with existing VITS architecture.
    """
    
    def __init__(
        self,
        vits_config: Dict[str, Any],
        speaker_encoder: Optional[PiperSpeakerEncoder] = None,
        speaker_embedding_dim: int = 256,
        enable_voice_cloning: bool = True
    ):
        super().__init__()
        
        self.enable_voice_cloning = enable_voice_cloning
        self.speaker_embedding_dim = speaker_embedding_dim
        
        # Original VITS model
        self.vits = SynthesizerTrn(**vits_config)
        
        # Speaker encoder for voice cloning
        if enable_voice_cloning:
            if speaker_encoder is None:
                self.speaker_encoder = PiperSpeakerEncoder(
                    embedding_dim=speaker_embedding_dim
                )
            else:
                self.speaker_encoder = speaker_encoder
            
            # Speaker projection layer
            vits_speaker_dim = getattr(self.vits, 'gin_channels', 192)
            self.speaker_projection = SpeakerProjection(
                speaker_embedding_dim=speaker_embedding_dim,
                vits_speaker_dim=vits_speaker_dim
            )
            
            # Timbre transfer components
            self.timbre_mixer = nn.Sequential(
                nn.Linear(vits_speaker_dim * 2, vits_speaker_dim),
                nn.Tanh()
            )
        
        self.n_speakers = getattr(self.vits, 'n_speakers', 1)
    
    def extract_speaker_embedding(
        self,
        reference_audio: torch.Tensor,
        sample_rate: Optional[int] = None
    ) -> torch.Tensor:
        """Extract speaker embedding from reference audio."""
        if not self.enable_voice_cloning:
            raise ValueError("Voice cloning is not enabled")
        
        return self.speaker_encoder.extract_embedding(reference_audio, sample_rate)
    
    def forward(
        self,
        x: torch.Tensor,
        x_lengths: torch.Tensor,
        y: Optional[torch.Tensor] = None,
        y_lengths: Optional[torch.Tensor] = None,
        speaker_id: Optional[torch.Tensor] = None,
        reference_audio: Optional[torch.Tensor] = None,
        speaker_embedding: Optional[torch.Tensor] = None,
        timbre_strength: float = 1.0,
        **kwargs
    ) -> Tuple[torch.Tensor, ...]:
        """
        Forward pass with voice cloning support.
        
        Args:
            x: Input phoneme sequence [batch_size, seq_len]
            x_lengths: Input sequence lengths [batch_size]
            y: Target mel spectrogram [batch_size, mel_bins, time] (training only)
            y_lengths: Target lengths [batch_size] (training only)
            speaker_id: Original speaker ID [batch_size] (optional)
            reference_audio: Reference audio for voice cloning [batch_size, audio_len]
            speaker_embedding: Pre-computed speaker embedding [batch_size, emb_dim]
            timbre_strength: Strength of voice cloning (0.0 to 1.0)
        
        Returns:
            Tuple of (mel_output, audio_output, ...)
        """
        batch_size = x.size(0)
        
        # Determine speaker representation
        if self.enable_voice_cloning and (reference_audio is not None or speaker_embedding is not None):
            # Voice cloning mode
            if speaker_embedding is None:
                speaker_embedding = self.extract_speaker_embedding(reference_audio)
            
            # Project to VITS speaker space
            cloned_speaker_emb = self.speaker_projection(speaker_embedding)
            
            # Mix with original speaker if available
            if speaker_id is not None and timbre_strength < 1.0:
                # Get original speaker embedding
                original_speaker_emb = self.vits.emb_g(speaker_id)
                
                # Mix embeddings
                mixed_speaker_emb = self.timbre_mixer(torch.cat([
                    cloned_speaker_emb * timbre_strength,
                    original_speaker_emb * (1.0 - timbre_strength)
                ], dim=-1))
                
                final_speaker_emb = mixed_speaker_emb
            else:
                final_speaker_emb = cloned_speaker_emb
            
            # Override speaker embedding in VITS
            # This requires modifying the VITS forward pass
            return self._forward_with_speaker_embedding(
                x, x_lengths, y, y_lengths, final_speaker_emb, **kwargs
            )
        
        else:
            # Standard VITS forward pass
            return self.vits(x, x_lengths, y, y_lengths, speaker_id, **kwargs)
    
    def _forward_with_speaker_embedding(
        self,
        x: torch.Tensor,
        x_lengths: torch.Tensor,
        y: Optional[torch.Tensor],
        y_lengths: Optional[torch.Tensor],
        speaker_embedding: torch.Tensor,
        **kwargs
    ) -> Tuple[torch.Tensor, ...]:
        """Forward pass with custom speaker embedding."""
        # This is a simplified version - you'd need to modify the actual VITS forward
        # to accept external speaker embeddings
        
        # For now, we'll use the closest speaker ID approach
        if hasattr(self.vits, 'emb_g') and self.vits.emb_g is not None:
            # Temporarily replace speaker embedding
            original_weight = self.vits.emb_g.weight.data.clone()
            
            # Find closest speaker or use first speaker
            speaker_id = torch.zeros(x.size(0), dtype=torch.long, device=x.device)
            
            # Replace with our custom embedding
            self.vits.emb_g.weight.data[0] = speaker_embedding[0]
            
            try:
                result = self.vits(x, x_lengths, y, y_lengths, speaker_id, **kwargs)
            finally:
                # Restore original weights
                self.vits.emb_g.weight.data = original_weight
            
            return result
        else:
            # Single speaker model
            return self.vits(x, x_lengths, y, y_lengths, None, **kwargs)
    
    def inference(
        self,
        x: torch.Tensor,
        x_lengths: torch.Tensor,
        reference_audio: Optional[torch.Tensor] = None,
        speaker_embedding: Optional[torch.Tensor] = None,
        speaker_id: Optional[torch.Tensor] = None,
        timbre_strength: float = 1.0,
        noise_scale: float = 0.667,
        noise_scale_w: float = 0.8,
        length_scale: float = 1.0,
        **kwargs
    ) -> torch.Tensor:
        """
        Inference with voice cloning.
        
        Args:
            x: Input phoneme sequence
            x_lengths: Input lengths
            reference_audio: Reference audio for voice cloning
            speaker_embedding: Pre-computed speaker embedding
            speaker_id: Original speaker ID
            timbre_strength: Voice cloning strength
            noise_scale: Noise scale for generation
            noise_scale_w: Noise scale for duration
            length_scale: Length scale for duration
        
        Returns:
            Generated audio waveform
        """
        self.eval()
        
        with torch.no_grad():
            if self.enable_voice_cloning and (reference_audio is not None or speaker_embedding is not None):
                # Voice cloning inference
                if speaker_embedding is None:
                    speaker_embedding = self.extract_speaker_embedding(reference_audio)
                
                # Project to VITS space
                cloned_speaker_emb = self.speaker_projection(speaker_embedding)
                
                # Use custom speaker embedding for inference
                return self._inference_with_speaker_embedding(
                    x, x_lengths, cloned_speaker_emb,
                    noise_scale, noise_scale_w, length_scale, **kwargs
                )
            else:
                # Standard inference
                return self.vits.infer(
                    x, x_lengths, speaker_id,
                    noise_scale, noise_scale_w, length_scale, **kwargs
                )
    
    def _inference_with_speaker_embedding(
        self,
        x: torch.Tensor,
        x_lengths: torch.Tensor,
        speaker_embedding: torch.Tensor,
        noise_scale: float,
        noise_scale_w: float,
        length_scale: float,
        **kwargs
    ) -> torch.Tensor:
        """Inference with custom speaker embedding."""
        # Similar to forward, this would need actual VITS modification
        # For now, use the replacement approach
        
        if hasattr(self.vits, 'emb_g') and self.vits.emb_g is not None:
            original_weight = self.vits.emb_g.weight.data.clone()
            speaker_id = torch.zeros(x.size(0), dtype=torch.long, device=x.device)
            
            self.vits.emb_g.weight.data[0] = speaker_embedding[0]
            
            try:
                result = self.vits.infer(
                    x, x_lengths, speaker_id,
                    noise_scale, noise_scale_w, length_scale, **kwargs
                )
            finally:
                self.vits.emb_g.weight.data = original_weight
            
            return result
        else:
            return self.vits.infer(
                x, x_lengths, None,
                noise_scale, noise_scale_w, length_scale, **kwargs
            )
    
    def enable_voice_cloning_mode(self):
        """Enable voice cloning capabilities."""
        self.enable_voice_cloning = True
        if hasattr(self, 'speaker_encoder'):
            self.speaker_encoder.eval_mode()
    
    def disable_voice_cloning_mode(self):
        """Disable voice cloning capabilities."""
        self.enable_voice_cloning = False
    
    def get_speaker_encoder(self) -> Optional[PiperSpeakerEncoder]:
        """Get the speaker encoder."""
        return getattr(self, 'speaker_encoder', None)

def create_vits_voice_cloning_model(
    vits_config: Dict[str, Any],
    speaker_embedding_dim: int = 256,
    enable_voice_cloning: bool = True
) -> VITSVoiceCloning:
    """Create a VITS model with voice cloning capabilities."""
    return VITSVoiceCloning(
        vits_config=vits_config,
        speaker_embedding_dim=speaker_embedding_dim,
        enable_voice_cloning=enable_voice_cloning
    )
