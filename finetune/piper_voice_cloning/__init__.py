"""
Piper Voice Cloning Module

This module extends Piper TTS with native voice cloning and timbre transfer capabilities.
It integrates speaker encoding directly into the VITS architecture.
"""

from .speaker_encoder import PiperSpeakerEncoder, create_speaker_encoder
from .vits_voice_cloning import VITSVoiceCloning, create_vits_voice_cloning_model

__version__ = "1.0.0"
__all__ = [
    "PiperSpeakerEncoder",
    "create_speaker_encoder", 
    "VITSVoiceCloning",
    "create_vits_voice_cloning_model"
]
