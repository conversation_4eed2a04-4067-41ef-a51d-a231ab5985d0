#!/usr/bin/env python3
"""
Advanced Emotion and Style Controller for Piper TTS.
Based on EmoSphere-TTS and UMETTS research for realistic emotional speech.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class EmotionType(Enum):
    """Supported emotion types."""
    NEUTRAL = "neutral"
    HAPPY = "happy"
    SAD = "sad"
    ANGRY = "angry"
    FEAR = "fear"
    SURPRISE = "surprise"
    DISGUST = "disgust"
    EXCITED = "excited"
    CALM = "calm"
    CONFIDENT = "confident"
    WHISPER = "whisper"
    SHOUTING = "shouting"

class SpeakingStyle(Enum):
    """Supported speaking styles."""
    CONVERSATIONAL = "conversational"
    FORMAL = "formal"
    CASUAL = "casual"
    DRAMATIC = "dramatic"
    STORYTELLING = "storytelling"
    NEWS_READING = "news_reading"
    POETRY = "poetry"
    CHILD_LIKE = "child_like"
    ELDERLY = "elderly"
    ROBOTIC = "robotic"

class SphericalEmotionEncoder(nn.Module):
    """
    Spherical emotion encoder based on EmoSphere-TTS.
    Maps emotions to a continuous spherical space for smooth interpolation.
    """
    
    def __init__(
        self,
        mel_dim: int = 80,
        emotion_dim: int = 256,
        sphere_radius: float = 1.0
    ):
        super().__init__()
        
        self.emotion_dim = emotion_dim
        self.sphere_radius = sphere_radius
        
        # Emotion feature extractor
        self.emotion_encoder = nn.Sequential(
            nn.Conv1d(mel_dim, 128, 3, padding=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 256, 3, stride=2, padding=1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Conv1d(256, 512, 3, stride=2, padding=1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten(),
            nn.Linear(512, emotion_dim),
            nn.Tanh()  # Constrain to sphere
        )
        
        # Emotion intensity predictor
        self.intensity_predictor = nn.Sequential(
            nn.Linear(emotion_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 1),
            nn.Sigmoid()  # Intensity between 0 and 1
        )
        
        # Predefined emotion anchors on sphere
        self.register_emotion_anchors()
    
    def register_emotion_anchors(self):
        """Register predefined emotion positions on the sphere."""
        # Create emotion anchors in spherical coordinates
        emotions = list(EmotionType)
        num_emotions = len(emotions)
        
        # Distribute emotions evenly on sphere surface
        emotion_anchors = []
        for i, emotion in enumerate(emotions):
            # Spherical coordinates: (theta, phi)
            theta = 2 * np.pi * i / num_emotions  # Azimuth
            phi = np.pi * (0.5 + 0.3 * np.sin(2 * np.pi * i / num_emotions))  # Elevation
            
            # Convert to Cartesian coordinates
            x = self.sphere_radius * np.sin(phi) * np.cos(theta)
            y = self.sphere_radius * np.sin(phi) * np.sin(theta)
            z = self.sphere_radius * np.cos(phi)
            
            emotion_anchors.append([x, y, z])
        
        # Register as buffer (not trainable)
        self.register_buffer(
            'emotion_anchors',
            torch.tensor(emotion_anchors, dtype=torch.float32)
        )
        
        # Create emotion name mapping
        self.emotion_to_idx = {emotion.value: i for i, emotion in enumerate(emotions)}
    
    def forward(self, mel_spec: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Extract emotion vector and intensity from mel spectrogram.
        
        Args:
            mel_spec: [batch, mel_dim, time]
        
        Returns:
            Dict with emotion_vector and intensity
        """
        # Extract emotion features
        emotion_vector = self.emotion_encoder(mel_spec)
        
        # Normalize to sphere surface
        emotion_vector = F.normalize(emotion_vector, p=2, dim=1) * self.sphere_radius
        
        # Predict intensity
        intensity = self.intensity_predictor(emotion_vector)
        
        return {
            'emotion_vector': emotion_vector,
            'intensity': intensity
        }
    
    def get_emotion_anchor(self, emotion: Union[str, EmotionType]) -> torch.Tensor:
        """Get predefined emotion anchor."""
        if isinstance(emotion, EmotionType):
            emotion = emotion.value
        
        idx = self.emotion_to_idx[emotion]
        return self.emotion_anchors[idx]
    
    def interpolate_emotions(
        self,
        emotion1: Union[str, EmotionType],
        emotion2: Union[str, EmotionType],
        weight: float = 0.5
    ) -> torch.Tensor:
        """Interpolate between two emotions on sphere surface."""
        anchor1 = self.get_emotion_anchor(emotion1)
        anchor2 = self.get_emotion_anchor(emotion2)
        
        # Spherical linear interpolation (SLERP)
        dot_product = torch.dot(anchor1, anchor2)
        dot_product = torch.clamp(dot_product, -1.0, 1.0)
        
        theta = torch.acos(dot_product)
        
        if theta.abs() < 1e-6:
            # Vectors are nearly identical
            return anchor1
        
        sin_theta = torch.sin(theta)
        w1 = torch.sin((1 - weight) * theta) / sin_theta
        w2 = torch.sin(weight * theta) / sin_theta
        
        interpolated = w1 * anchor1 + w2 * anchor2
        return F.normalize(interpolated, p=2, dim=0) * self.sphere_radius

class ProsodyController(nn.Module):
    """
    Advanced prosody controller for rhythm, stress, and intonation.
    """
    
    def __init__(
        self,
        mel_dim: int = 80,
        prosody_dim: int = 128,
        num_prosody_features: int = 6  # pitch, energy, duration, stress, rhythm, intonation
    ):
        super().__init__()
        
        self.prosody_dim = prosody_dim
        self.num_prosody_features = num_prosody_features
        
        # Prosody feature extractor
        self.prosody_encoder = nn.Sequential(
            nn.Conv1d(mel_dim, 64, 5, padding=2),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Conv1d(64, 128, 5, stride=2, padding=2),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 256, 5, stride=2, padding=2),
            nn.BatchNorm1d(256),
            nn.ReLU(),
        )
        
        # Temporal modeling with LSTM
        self.temporal_model = nn.LSTM(
            input_size=256,
            hidden_size=128,
            num_layers=2,
            batch_first=True,
            bidirectional=True
        )
        
        # Prosody feature heads
        self.prosody_heads = nn.ModuleDict({
            'pitch': nn.Linear(256, 1),
            'energy': nn.Linear(256, 1),
            'duration': nn.Linear(256, 1),
            'stress': nn.Linear(256, 1),
            'rhythm': nn.Linear(256, 1),
            'intonation': nn.Linear(256, 1)
        })
        
        # Global prosody embedding
        self.global_prosody_proj = nn.Linear(256, prosody_dim)
    
    def forward(self, mel_spec: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Extract prosody features from mel spectrogram.
        
        Args:
            mel_spec: [batch, mel_dim, time]
        
        Returns:
            Dict with prosody features
        """
        batch_size, mel_dim, time_steps = mel_spec.shape
        
        # Extract prosody features
        prosody_features = self.prosody_encoder(mel_spec)  # [batch, 256, time//4]
        
        # Temporal modeling
        prosody_features = prosody_features.transpose(1, 2)  # [batch, time//4, 256]
        lstm_out, _ = self.temporal_model(prosody_features)  # [batch, time//4, 256]
        
        # Extract specific prosody features
        prosody_dict = {}
        for feature_name, head in self.prosody_heads.items():
            feature_values = head(lstm_out)  # [batch, time//4, 1]
            prosody_dict[feature_name] = feature_values.squeeze(-1)  # [batch, time//4]
        
        # Global prosody embedding
        global_prosody = torch.mean(lstm_out, dim=1)  # [batch, 256]
        prosody_embedding = self.global_prosody_proj(global_prosody)  # [batch, prosody_dim]
        
        prosody_dict['global_embedding'] = prosody_embedding
        prosody_dict['temporal_features'] = lstm_out
        
        return prosody_dict

class TimbreTransferModule(nn.Module):
    """
    Advanced timbre transfer module based on RAVE and neural audio synthesis.
    """
    
    def __init__(
        self,
        mel_dim: int = 80,
        timbre_dim: int = 192,
        num_timbre_components: int = 8
    ):
        super().__init__()
        
        self.timbre_dim = timbre_dim
        self.num_timbre_components = num_timbre_components
        
        # Timbre analysis network
        self.timbre_analyzer = nn.Sequential(
            nn.Conv1d(mel_dim, 128, 7, padding=3),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Conv1d(128, 256, 7, stride=2, padding=3),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Conv1d(256, 512, 7, stride=2, padding=3),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.AdaptiveAvgPool1d(1),
            nn.Flatten()
        )
        
        # Timbre component decomposition
        self.timbre_decomposer = nn.ModuleList([
            nn.Sequential(
                nn.Linear(512, 256),
                nn.ReLU(),
                nn.Linear(256, timbre_dim // num_timbre_components)
            )
            for _ in range(num_timbre_components)
        ])
        
        # Timbre mixing network
        self.timbre_mixer = nn.Sequential(
            nn.Linear(timbre_dim, 256),
            nn.ReLU(),
            nn.Linear(256, num_timbre_components),
            nn.Softmax(dim=1)
        )
        
        # Timbre transfer network
        self.timbre_transfer = nn.Sequential(
            nn.Linear(timbre_dim * 2, 512),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, timbre_dim),
            nn.Tanh()
        )
    
    def extract_timbre(self, mel_spec: torch.Tensor) -> torch.Tensor:
        """Extract timbre embedding from mel spectrogram."""
        # Analyze timbre
        timbre_features = self.timbre_analyzer(mel_spec)
        
        # Decompose into components
        timbre_components = []
        for decomposer in self.timbre_decomposer:
            component = decomposer(timbre_features)
            timbre_components.append(component)
        
        # Concatenate components
        timbre_embedding = torch.cat(timbre_components, dim=1)
        
        return timbre_embedding
    
    def transfer_timbre(
        self,
        source_timbre: torch.Tensor,
        target_timbre: torch.Tensor,
        transfer_strength: float = 1.0
    ) -> torch.Tensor:
        """Transfer timbre from target to source."""
        # Concatenate source and target
        combined_timbre = torch.cat([source_timbre, target_timbre], dim=1)
        
        # Apply transfer network
        transferred_timbre = self.timbre_transfer(combined_timbre)
        
        # Mix with original based on strength
        final_timbre = (
            source_timbre * (1 - transfer_strength) +
            transferred_timbre * transfer_strength
        )
        
        return final_timbre
    
    def adaptive_timbre_mixing(
        self,
        timbre_components: List[torch.Tensor],
        mixing_weights: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """Adaptively mix timbre components."""
        if mixing_weights is None:
            # Compute adaptive weights
            combined_timbre = torch.cat(timbre_components, dim=1)
            mixing_weights = self.timbre_mixer(combined_timbre)
        
        # Mix components
        mixed_timbre = torch.zeros_like(timbre_components[0])
        for i, component in enumerate(timbre_components):
            mixed_timbre += component * mixing_weights[:, i:i+1]
        
        return mixed_timbre

class UnifiedStyleEmotionController(nn.Module):
    """
    Unified controller combining emotion, style, prosody, and timbre.
    Single-pass processing for minimal latency.
    """
    
    def __init__(
        self,
        mel_dim: int = 80,
        emotion_dim: int = 256,
        prosody_dim: int = 128,
        timbre_dim: int = 192,
        unified_dim: int = 512
    ):
        super().__init__()
        
        # Individual controllers
        self.emotion_controller = SphericalEmotionEncoder(mel_dim, emotion_dim)
        self.prosody_controller = ProsodyController(mel_dim, prosody_dim)
        self.timbre_controller = TimbreTransferModule(mel_dim, timbre_dim)
        
        # Unified projection
        total_dim = emotion_dim + prosody_dim + timbre_dim
        self.unified_projection = nn.Sequential(
            nn.Linear(total_dim, unified_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(unified_dim, unified_dim),
            nn.Tanh()
        )
        
        # Adaptive weighting
        self.adaptive_weights = nn.Sequential(
            nn.Linear(total_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 3),  # emotion, prosody, timbre weights
            nn.Softmax(dim=1)
        )
    
    def forward(
        self,
        mel_spec: torch.Tensor,
        target_emotion: Optional[str] = None,
        emotion_intensity: float = 1.0,
        prosody_strength: float = 1.0,
        timbre_strength: float = 1.0
    ) -> Dict[str, torch.Tensor]:
        """
        Unified style and emotion control.
        
        Args:
            mel_spec: Input mel spectrogram [batch, mel_dim, time]
            target_emotion: Target emotion for transfer
            emotion_intensity: Emotion intensity (0-1)
            prosody_strength: Prosody transfer strength (0-1)
            timbre_strength: Timbre transfer strength (0-1)
        
        Returns:
            Dict with unified control embeddings
        """
        # Extract individual features
        emotion_features = self.emotion_controller(mel_spec)
        prosody_features = self.prosody_controller(mel_spec)
        timbre_embedding = self.timbre_controller.extract_timbre(mel_spec)
        
        # Apply target emotion if specified
        if target_emotion:
            target_emotion_vector = self.emotion_controller.get_emotion_anchor(target_emotion)
            target_emotion_vector = target_emotion_vector.unsqueeze(0).repeat(mel_spec.size(0), 1)
            
            # Interpolate with target emotion
            emotion_features['emotion_vector'] = self.emotion_controller.interpolate_emotions(
                emotion_features['emotion_vector'],
                target_emotion_vector,
                emotion_intensity
            )
        
        # Combine all features
        combined_features = torch.cat([
            emotion_features['emotion_vector'],
            prosody_features['global_embedding'],
            timbre_embedding
        ], dim=1)
        
        # Compute adaptive weights
        adaptive_weights = self.adaptive_weights(combined_features)
        
        # Apply strength controls
        weighted_features = torch.cat([
            emotion_features['emotion_vector'] * adaptive_weights[:, 0:1] * emotion_intensity,
            prosody_features['global_embedding'] * adaptive_weights[:, 1:2] * prosody_strength,
            timbre_embedding * adaptive_weights[:, 2:3] * timbre_strength
        ], dim=1)
        
        # Unified projection
        unified_embedding = self.unified_projection(weighted_features)
        
        return {
            'unified_embedding': unified_embedding,
            'emotion_features': emotion_features,
            'prosody_features': prosody_features,
            'timbre_embedding': timbre_embedding,
            'adaptive_weights': adaptive_weights
        }

def create_style_emotion_controller(
    mel_dim: int = 80,
    unified_dim: int = 512
) -> UnifiedStyleEmotionController:
    """Create unified style and emotion controller."""
    return UnifiedStyleEmotionController(
        mel_dim=mel_dim,
        unified_dim=unified_dim
    )
