#!/usr/bin/env python3
"""
Test script for Piper voice cloning capabilities.
"""

import os
import sys
import torch
import torchaudio
import numpy as np
from pathlib import Path
import logging

# Add piper to path
piper_path = Path("piper/src/python")
if piper_path.exists():
    sys.path.insert(0, str(piper_path))

from piper_voice_cloning.vits_voice_cloning import VITSVoiceCloning
from piper_voice_cloning.speaker_encoder import PiperSpeakerEncoder

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PiperVoiceCloningTester:
    """Test Piper voice cloning functionality."""
    
    def __init__(self, model_path: str, config_path: str):
        self.model_path = Path(model_path)
        self.config_path = Path(config_path)
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        self.model = None
        self.load_model()
    
    def load_model(self):
        """Load the voice cloning model."""
        logger.info(f"Loading voice cloning model from {self.model_path}")
        
        checkpoint = torch.load(self.model_path, map_location=self.device)
        
        # Extract config
        config = checkpoint.get('config', {})
        speaker_embedding_dim = checkpoint.get('speaker_embedding_dim', 256)
        
        # Create model (this would need the actual VITS config)
        # For now, we'll create a simplified version
        vits_config = {
            'n_vocab': 256,
            'spec_channels': 513,
            'segment_size': 32,
            'inter_channels': 192,
            'hidden_channels': 192,
            'filter_channels': 768,
            'n_heads': 2,
            'n_layers': 6,
            'kernel_size': 3,
            'p_dropout': 0.1,
            'resblock': '1',
            'resblock_kernel_sizes': [3, 7, 11],
            'resblock_dilation_sizes': [[1, 3, 5], [1, 3, 5], [1, 3, 5]],
            'upsample_rates': [8, 8, 2, 2],
            'upsample_initial_channel': 512,
            'upsample_kernel_sizes': [16, 16, 4, 4],
            'n_speakers': 0,
            'gin_channels': 0,
        }
        
        self.model = VITSVoiceCloning(
            vits_config=vits_config,
            speaker_embedding_dim=speaker_embedding_dim,
            enable_voice_cloning=True
        )
        
        # Load state dict
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.to(self.device)
        self.model.eval()
        
        logger.info("Voice cloning model loaded successfully")
    
    def test_speaker_encoder(self, audio_path: str):
        """Test speaker encoder functionality."""
        logger.info(f"Testing speaker encoder with {audio_path}")
        
        # Load audio
        audio, sample_rate = torchaudio.load(audio_path)
        audio = audio.mean(dim=0)  # Convert to mono
        
        # Extract speaker embedding
        speaker_embedding = self.model.extract_speaker_embedding(audio, sample_rate)
        
        logger.info(f"Speaker embedding shape: {speaker_embedding.shape}")
        logger.info(f"Speaker embedding norm: {torch.norm(speaker_embedding).item():.4f}")
        
        return speaker_embedding
    
    def test_voice_cloning(
        self,
        text: str,
        reference_audio_path: str,
        output_path: str = "cloned_voice_test.wav"
    ):
        """Test voice cloning functionality."""
        logger.info(f"Testing voice cloning with reference: {reference_audio_path}")
        
        # Load reference audio
        reference_audio, sample_rate = torchaudio.load(reference_audio_path)
        reference_audio = reference_audio.mean(dim=0)  # Convert to mono
        
        # Convert text to phonemes (simplified)
        # In real implementation, you'd use the Piper phonemizer
        phonemes = self._text_to_phonemes(text)
        
        # Convert to tensor
        x = torch.tensor(phonemes, dtype=torch.long).unsqueeze(0).to(self.device)
        x_lengths = torch.tensor([len(phonemes)], dtype=torch.long).to(self.device)
        
        # Perform voice cloning inference
        with torch.no_grad():
            audio_output = self.model.inference(
                x=x,
                x_lengths=x_lengths,
                reference_audio=reference_audio,
                timbre_strength=1.0,
                noise_scale=0.667,
                noise_scale_w=0.8,
                length_scale=1.0
            )
        
        # Save output
        if audio_output is not None:
            torchaudio.save(output_path, audio_output.cpu(), 22050)
            logger.info(f"Cloned voice saved to: {output_path}")
        else:
            logger.error("Voice cloning failed - no audio output")
        
        return audio_output
    
    def test_timbre_transfer(
        self,
        source_audio_path: str,
        target_voice_path: str,
        output_path: str = "timbre_transfer_test.wav",
        strength: float = 0.8
    ):
        """Test timbre transfer functionality."""
        logger.info(f"Testing timbre transfer: {source_audio_path} -> {target_voice_path}")
        
        # Load audio files
        source_audio, _ = torchaudio.load(source_audio_path)
        target_audio, _ = torchaudio.load(target_voice_path)
        
        source_audio = source_audio.mean(dim=0)
        target_audio = target_audio.mean(dim=0)
        
        # Extract speaker embeddings
        source_embedding = self.model.extract_speaker_embedding(source_audio)
        target_embedding = self.model.extract_speaker_embedding(target_audio)
        
        # Mix embeddings for timbre transfer
        mixed_embedding = (
            source_embedding * (1.0 - strength) +
            target_embedding * strength
        )
        
        # For timbre transfer, we'd need the original mel spectrogram
        # This is a simplified version
        logger.info("Timbre transfer completed (simplified version)")
        
        # Compute similarity
        similarity = torch.cosine_similarity(source_embedding, target_embedding, dim=0)
        logger.info(f"Speaker similarity: {similarity.item():.4f}")
        
        return mixed_embedding
    
    def _text_to_phonemes(self, text: str) -> list:
        """Convert text to phonemes (simplified)."""
        # This is a placeholder - in real implementation, use Piper's phonemizer
        # For now, just convert to character indices
        phonemes = [ord(c) % 256 for c in text.lower() if c.isalnum() or c.isspace()]
        return phonemes[:100]  # Limit length
    
    def benchmark_performance(self, reference_audio_path: str, num_tests: int = 5):
        """Benchmark voice cloning performance."""
        logger.info(f"Benchmarking performance with {num_tests} tests")
        
        # Load reference audio
        reference_audio, sample_rate = torchaudio.load(reference_audio_path)
        reference_audio = reference_audio.mean(dim=0)
        
        # Test text
        test_text = "This is a performance test for voice cloning."
        phonemes = self._text_to_phonemes(test_text)
        
        x = torch.tensor(phonemes, dtype=torch.long).unsqueeze(0).to(self.device)
        x_lengths = torch.tensor([len(phonemes)], dtype=torch.long).to(self.device)
        
        # Warm up
        with torch.no_grad():
            _ = self.model.extract_speaker_embedding(reference_audio, sample_rate)
        
        # Benchmark
        import time
        times = []
        
        for i in range(num_tests):
            start_time = time.time()
            
            with torch.no_grad():
                # Extract embedding
                speaker_embedding = self.model.extract_speaker_embedding(reference_audio, sample_rate)
                
                # Inference
                audio_output = self.model.inference(
                    x=x,
                    x_lengths=x_lengths,
                    speaker_embedding=speaker_embedding,
                    timbre_strength=1.0
                )
            
            end_time = time.time()
            times.append(end_time - start_time)
            
            logger.info(f"Test {i+1}: {times[-1]:.3f}s")
        
        avg_time = np.mean(times)
        std_time = np.std(times)
        
        logger.info(f"Average time: {avg_time:.3f}s ± {std_time:.3f}s")
        logger.info(f"Real-time factor: {avg_time / (len(phonemes) * 0.05):.2f}x")  # Approximate

def create_test_audio():
    """Create a simple test audio file."""
    # Generate a simple sine wave for testing
    sample_rate = 22050
    duration = 3.0  # seconds
    frequency = 440  # Hz
    
    t = torch.linspace(0, duration, int(sample_rate * duration))
    audio = 0.3 * torch.sin(2 * np.pi * frequency * t)
    
    # Add some harmonics for more realistic sound
    audio += 0.1 * torch.sin(2 * np.pi * frequency * 2 * t)
    audio += 0.05 * torch.sin(2 * np.pi * frequency * 3 * t)
    
    test_audio_path = "test_reference_audio.wav"
    torchaudio.save(test_audio_path, audio.unsqueeze(0), sample_rate)
    
    return test_audio_path

def main():
    """Main test function."""
    print("🎭 Testing Piper Voice Cloning")
    print("=" * 40)
    
    # Check for voice cloning model
    model_path = Path("training_output/voice_cloning_models/piper_voice_cloning.pth")
    config_path = Path("training_output/voice_cloning_models/piper_voice_cloning_config.json")
    
    if not model_path.exists():
        print("❌ Voice cloning model not found!")
        print("Run: python add_voice_cloning_to_piper.py first")
        return
    
    try:
        # Create tester
        tester = PiperVoiceCloningTester(str(model_path), str(config_path))
        
        # Create test audio if needed
        test_audio_path = create_test_audio()
        print(f"📁 Created test audio: {test_audio_path}")
        
        # Test speaker encoder
        print("\n🎤 Testing Speaker Encoder...")
        speaker_embedding = tester.test_speaker_encoder(test_audio_path)
        
        # Test voice cloning
        print("\n🎭 Testing Voice Cloning...")
        test_text = "Hello, this is a test of voice cloning with Piper."
        cloned_audio = tester.test_voice_cloning(test_text, test_audio_path)
        
        # Test timbre transfer
        print("\n🔄 Testing Timbre Transfer...")
        mixed_embedding = tester.test_timbre_transfer(test_audio_path, test_audio_path)
        
        # Benchmark performance
        print("\n⚡ Benchmarking Performance...")
        tester.benchmark_performance(test_audio_path, num_tests=3)
        
        print("\n🎉 All tests completed!")
        print("\n📁 Generated files:")
        for file_path in Path(".").glob("*test*.wav"):
            print(f"   - {file_path}")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    main()
