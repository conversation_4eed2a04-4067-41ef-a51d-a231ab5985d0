import React from 'react';

const Header = ({ apiStatus }) => {
  const getStatusColor = () => {
    switch (apiStatus) {
      case 'ready':
        return 'text-green-500';
      case 'error':
        return 'text-red-500';
      default:
        return 'text-yellow-500';
    }
  };

  const getStatusText = () => {
    switch (apiStatus) {
      case 'ready':
        return 'API Ready';
      case 'error':
        return 'API Error';
      default:
        return 'Checking API...';
    }
  };

  return (
    <header className="glass-header border-b border-white/20 shadow-lg">
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
              <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
              </svg>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-800 tracking-tight">Piper TTS</h1>
              <p className="text-sm text-gray-600 font-medium">Neural Text-to-Speech in Nepali</p>
            </div>
          </div>

          <div className="flex items-center space-x-3 px-4 py-2 glass-card rounded-full">
            <div className={`w-3 h-3 rounded-full ${getStatusColor().replace('text-', 'bg-')} animate-pulse`}></div>
            <span className={`text-sm font-semibold ${getStatusColor()}`}>
              {getStatusText()}
            </span>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
