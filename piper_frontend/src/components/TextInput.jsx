import React from 'react';

const TextInput = ({ text, setText, onSynthesize, isLoading, disabled }) => {
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      onSynthesize();
    }
  };

  const characterCount = text.length;
  const maxCharacters = 1000;

  return (
    <div className="backdrop-blur-md bg-white/20 rounded-2xl border border-white/30 shadow-xl p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          <h2 className="text-lg font-semibold text-gray-800">Text Input</h2>
        </div>
        <div className="text-sm text-gray-600">
          {characterCount}/{maxCharacters}
        </div>
      </div>

      <div className="space-y-4">
        <div className="relative">
          <textarea
            value={text}
            onChange={(e) => setText(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Enter your text here to synthesize speech... (Ctrl+Enter to generate)"
            className="w-full h-32 p-4 bg-white/30 border border-white/30 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent placeholder-gray-500 text-gray-800 backdrop-blur-sm"
            maxLength={maxCharacters}
            disabled={disabled}
          />
          {text && (
            <button
              onClick={() => setText('')}
              className="absolute top-3 right-3 p-1 text-gray-400 hover:text-gray-600 transition-colors"
              title="Clear text"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>

        <button
          onClick={onSynthesize}
          disabled={!text.trim() || isLoading || disabled}
          className="w-full py-3 px-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2"
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span>Generating Speech...</span>
            </>
          ) : (
            <>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
              </svg>
              <span>Generate Speech</span>
            </>
          )}
        </button>

        <div className="text-xs text-gray-600 text-center">
          Press Ctrl+Enter to quickly generate speech
        </div>
      </div>
    </div>
  );
};

export default TextInput;
