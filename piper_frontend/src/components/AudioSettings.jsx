import React from 'react';

const AudioSettings = ({
  speakers,
  speakerId,
  setSpeakerId,
  speechRate,
  setSpeechRate,
  noiseScale,
  setNoiseScale,
  noiseW,
  setNoiseW,
  sentenceSilence,
  setSentenceSilence
}) => {
  const SliderControl = ({ label, value, onChange, min, max, step, unit = '' }) => (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <label className="text-sm font-medium text-gray-700">{label}</label>
        <span className="text-sm text-gray-600 bg-white/30 px-2 py-1 rounded">
          {value}{unit}
        </span>
      </div>
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => onChange(parseFloat(e.target.value))}
        className="w-full h-2 bg-white/30 rounded-full appearance-none cursor-pointer slider"
      />
    </div>
  );

  return (
    <div className="backdrop-blur-md bg-white/20 rounded-2xl border border-white/30 shadow-xl p-6">
      <div className="flex items-center space-x-2 mb-6">
        <svg className="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
        </svg>
        <h2 className="text-lg font-semibold text-gray-800">Audio Settings</h2>
      </div>

      <div className="space-y-6">
        {/* Speaker Selection */}
        {speakers.length > 1 && (
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Speaker</label>
            <select
              value={speakerId}
              onChange={(e) => setSpeakerId(parseInt(e.target.value))}
              className="w-full p-3 bg-white/30 border border-white/30 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent text-gray-800 backdrop-blur-sm"
            >
              {speakers.map((speaker, index) => (
                <option key={index} value={index}>
                  Speaker {index + 1}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Speech Rate */}
        <SliderControl
          label="Speech Rate"
          value={speechRate}
          onChange={setSpeechRate}
          min={0.5}
          max={2.0}
          step={0.1}
          unit="x"
        />

        {/* Noise Scale */}
        <SliderControl
          label="Noise Scale"
          value={noiseScale}
          onChange={setNoiseScale}
          min={0.0}
          max={1.0}
          step={0.01}
        />

        {/* Noise W */}
        <SliderControl
          label="Noise W"
          value={noiseW}
          onChange={setNoiseW}
          min={0.0}
          max={1.0}
          step={0.01}
        />

        {/* Sentence Silence */}
        <SliderControl
          label="Sentence Silence"
          value={sentenceSilence}
          onChange={setSentenceSilence}
          min={0.0}
          max={2.0}
          step={0.1}
          unit="s"
        />

        {/* Reset Button */}
        <button
          onClick={() => {
            setSpeechRate(1.0);
            setNoiseScale(0.667);
            setNoiseW(0.8);
            setSentenceSilence(0.2);
          }}
          className="w-full py-2 px-4 bg-white/20 text-gray-700 rounded-lg hover:bg-white/30 transition-colors text-sm"
        >
          Reset to Defaults
        </button>
      </div>
    </div>
  );
};

export default AudioSettings;
