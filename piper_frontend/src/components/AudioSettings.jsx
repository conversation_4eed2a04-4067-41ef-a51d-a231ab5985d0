import React from 'react';

const AudioSettings = ({
  speakers,
  speakerId,
  setSpeakerId,
  speechRate,
  setSpeechRate,
  noiseScale,
  setNoiseScale,
  noiseW,
  setNoiseW,
  sentenceSilence,
  setSentenceSilence
}) => {
  const SliderControl = ({ label, value, onChange, min, max, step, unit = '' }) => (
    <div className="space-y-1">
      <div className="flex justify-between items-center">
        <label className="text-xs font-medium text-gray-700">{label}</label>
        <span className="text-xs text-gray-600 bg-white/30 px-2 py-1 rounded">
          {value}{unit}
        </span>
      </div>
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => onChange(parseFloat(e.target.value))}
        className="w-full h-1 bg-white/30 rounded-full appearance-none cursor-pointer slider"
      />
    </div>
  );

  return (
    <div className="glass-card p-4 w-full h-full">
      <div className="flex items-center space-x-2 mb-4">
        <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-pink-500 rounded-md flex items-center justify-center">
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
          </svg>
        </div>
        <h2 className="text-lg font-bold text-gray-800">Audio Settings</h2>
      </div>

      <div className="space-y-4">
        {/* Voice Cloning Option */}
        <div className="glass-card p-4 border border-emerald-200">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-6 h-6 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-md flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
              </svg>
            </div>
            <h3 className="text-sm font-bold text-gray-800">Voice Cloning</h3>
          </div>
          <div className="space-y-3">
            <input
              type="file"
              accept="audio/*"
              className="w-full text-xs text-gray-600 file:mr-3 file:py-2 file:px-3 file:rounded-lg file:border-0 file:text-xs file:font-medium file:bg-emerald-50 file:text-emerald-700 hover:file:bg-emerald-100"
            />
            <button className="w-full py-2 px-3 bg-gradient-to-r from-emerald-500 to-teal-500 text-white text-xs font-medium rounded-lg hover:from-emerald-600 hover:to-teal-600 transition-all duration-200 shadow-md">
              Clone Voice
            </button>
          </div>
        </div>

        {/* Speaker Selection */}
        {speakers.length > 1 && (
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Speaker</label>
            <select
              value={speakerId}
              onChange={(e) => setSpeakerId(parseInt(e.target.value))}
              className="w-full p-3 bg-white/30 border border-white/30 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent text-gray-800 backdrop-blur-sm"
            >
              {speakers.map((speaker, index) => (
                <option key={index} value={index}>
                  Speaker {index + 1}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Speech Rate */}
        <SliderControl
          label="Speech Rate"
          value={speechRate}
          onChange={setSpeechRate}
          min={0.5}
          max={2.0}
          step={0.1}
          unit="x"
        />

        {/* Noise Scale */}
        <SliderControl
          label="Noise Scale"
          value={noiseScale}
          onChange={setNoiseScale}
          min={0.0}
          max={1.0}
          step={0.01}
        />

        {/* Noise W */}
        <SliderControl
          label="Noise W"
          value={noiseW}
          onChange={setNoiseW}
          min={0.0}
          max={1.0}
          step={0.01}
        />

        {/* Sentence Silence */}
        <SliderControl
          label="Sentence Silence"
          value={sentenceSilence}
          onChange={setSentenceSilence}
          min={0.0}
          max={2.0}
          step={0.1}
          unit="s"
        />

        {/* Reset Button */}
        <button
          onClick={() => {
            setSpeechRate(1.0);
            setNoiseScale(0.667);
            setNoiseW(0.8);
            setSentenceSilence(0.2);
          }}
          className="w-full py-2 px-4 bg-white/20 text-gray-700 rounded-lg hover:bg-white/30 transition-colors text-sm"
        >
          Reset to Defaults
        </button>
      </div>
    </div>
  );
};

export default AudioSettings;
