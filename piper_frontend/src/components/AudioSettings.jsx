import React from 'react';

const AudioSettings = ({
  speakers,
  speakerId,
  setSpeakerId,
  speechRate,
  setSpeechRate,
  noiseScale,
  setNoiseScale,
  noiseW,
  setNoiseW,
  sentenceSilence,
  setSentenceSilence,
  // Voice cloning props
  useVoiceCloning,
  setUseVoiceCloning,
  referenceAudio,
  setReferenceAudio,
  cloningModel,
  setCloningModel,
  cloningStrength,
  setCloningStrength
}) => {
  const SliderControl = ({ label, value, onChange, min, max, step, unit = '' }) => (
    <div className="space-y-1">
      <div className="flex justify-between items-center">
        <label className="text-xs font-medium text-gray-700">{label}</label>
        <span className="text-xs text-gray-600 bg-white/30 px-2 py-1 rounded">
          {value}{unit}
        </span>
      </div>
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => onChange(parseFloat(e.target.value))}
        className="w-full"
      />
    </div>
  );

  return (
    <div className="glass-card p-4 w-full h-full">
      <div className="flex items-center space-x-2 mb-4">
        <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-pink-500 rounded-md flex items-center justify-center">
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
          </svg>
        </div>
        <h2 className="text-lg font-bold text-gray-800">Audio Settings</h2>
      </div>

      <div className="space-y-4">
        {/* Voice Cloning Option */}
        <div className="glass-card p-3 border border-emerald-200">
          <div className="flex items-center space-x-2 mb-2">
            <input
              type="checkbox"
              id="useVoiceCloning"
              checked={useVoiceCloning}
              onChange={(e) => setUseVoiceCloning(e.target.checked)}
              className="w-4 h-4 text-emerald-600 bg-white/30 border-gray-300 rounded focus:ring-emerald-500"
            />
            <div className="w-5 h-5 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-md flex items-center justify-center">
              <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2" />
              </svg>
            </div>
            <label htmlFor="useVoiceCloning" className="text-xs font-bold text-gray-800 cursor-pointer">
              Voice Cloning
            </label>
          </div>

          {useVoiceCloning && (
            <div className="space-y-2">
              <input
                type="file"
                accept="audio/*"
                onChange={(e) => setReferenceAudio(e.target.files[0])}
                className="w-full text-xs text-gray-600 file:mr-2 file:py-1 file:px-2 file:rounded file:border-0 file:text-xs file:font-medium file:bg-emerald-50 file:text-emerald-700 hover:file:bg-emerald-100"
              />

              <select
                value={cloningModel}
                onChange={(e) => setCloningModel(e.target.value)}
                className="w-full p-2 bg-white/30 border border-white/30 rounded text-xs focus:outline-none focus:ring-1 focus:ring-emerald-400"
              >
                <option value="f5_tts">F5-TTS (Fast)</option>
                <option value="xtts_v2">XTTS v2 (High Quality)</option>
                <option value="openvoice_v2">OpenVoice v2 (Instant)</option>
              </select>

              <div className="space-y-1">
                <div className="flex justify-between items-center">
                  <label className="text-xs font-medium text-gray-700">Cloning Strength</label>
                  <span className="text-xs text-gray-600 bg-white/30 px-1 py-0.5 rounded">
                    {cloningStrength}
                  </span>
                </div>
                <input
                  type="range"
                  min={0.1}
                  max={2.0}
                  step={0.1}
                  value={cloningStrength}
                  onChange={(e) => setCloningStrength(parseFloat(e.target.value))}
                  className="w-full"
                />
              </div>
            </div>
          )}
        </div>

        {/* Speaker Selection */}
        {speakers.length > 1 && (
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Speaker</label>
            <select
              value={speakerId}
              onChange={(e) => setSpeakerId(parseInt(e.target.value))}
              className="w-full p-3 bg-white/30 border border-white/30 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent text-gray-800 backdrop-blur-sm"
            >
              {speakers.map((speaker, index) => (
                <option key={index} value={index}>
                  Speaker {index + 1}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Speech Rate */}
        <SliderControl
          label="Speech Rate"
          value={speechRate}
          onChange={setSpeechRate}
          min={0.5}
          max={2.0}
          step={0.1}
          unit="x"
        />

        {/* Noise Scale */}
        <SliderControl
          label="Noise Scale"
          value={noiseScale}
          onChange={setNoiseScale}
          min={0.0}
          max={1.0}
          step={0.01}
        />

        {/* Noise W */}
        <SliderControl
          label="Noise W"
          value={noiseW}
          onChange={setNoiseW}
          min={0.0}
          max={1.0}
          step={0.01}
        />

        {/* Sentence Silence */}
        <SliderControl
          label="Sentence Silence"
          value={sentenceSilence}
          onChange={setSentenceSilence}
          min={0.0}
          max={2.0}
          step={0.1}
          unit="s"
        />

        {/* Reset Button */}
        <button
          onClick={() => {
            setSpeechRate(1.0);
            setNoiseScale(0.667);
            setNoiseW(0.8);
            setSentenceSilence(0.2);
          }}
          className="w-full py-2 px-4 bg-white/20 text-gray-700 rounded-lg hover:bg-white/30 transition-colors text-sm"
        >
          Reset to Defaults
        </button>
      </div>
    </div>
  );
};

export default AudioSettings;
