import React from 'react';

const SampleTexts = ({ onSelectText }) => {
  const sampleTexts = [
    {
      category: "Nepali",
      texts: [
        "नमस्ते, म एक कृत्रिम बुद्धिमत्ता हुँ जसले नेपाली भाषामा बोल्न सक्छु।",
        "आज मौसम धेरै राम्रो छ। हामी बाहिर घुम्न जाऔं।",
        "नेपाल एक सुन्दर देश हो जहाँ हिमाल, पहाड र तराई छन्।",
        "शिक्षा मानव जीवनको सबैभन्दा महत्वपूर्ण भाग हो।"
      ]
    },
    {
      category: "Technical",
      texts: [
        "यो एक न्यूरल नेटवर्क आधारित टेक्स्ट टु स्पीच प्रणाली हो।",
        "कृत्रिम बुद्धिमत्ताले भविष्यमा धेरै क्षेत्रमा क्रान्ति ल्याउनेछ।",
        "डिजिटल प्रविधिले हाम्रो जीवनलाई सजिलो बनाएको छ।"
      ]
    }
  ];

  return (
    <div className="backdrop-blur-md bg-white/20 rounded-2xl border border-white/30 shadow-xl p-6">
      <div className="flex items-center space-x-2 mb-6">
        <svg className="w-5 h-5 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h2 className="text-lg font-semibold text-gray-800">Sample Texts</h2>
      </div>

      <div className="space-y-4">
        {sampleTexts.map((category, categoryIndex) => (
          <div key={categoryIndex} className="space-y-3">
            <h3 className="text-sm font-medium text-gray-700 border-b border-white/20 pb-1">
              {category.category}
            </h3>
            <div className="space-y-2">
              {category.texts.map((text, textIndex) => (
                <button
                  key={textIndex}
                  onClick={() => onSelectText(text)}
                  className="w-full p-3 text-left bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 hover:border-white/40 transition-all duration-200 text-sm text-gray-700 hover:text-gray-800 group"
                >
                  <div className="flex items-start justify-between">
                    <span className="flex-1 line-clamp-3">{text}</span>
                    <svg className="w-4 h-4 text-gray-400 group-hover:text-gray-600 ml-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                </button>
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 p-3 bg-white/10 rounded-lg border border-white/20">
        <p className="text-xs text-gray-600 text-center">
          Click on any sample text to use it for speech synthesis
        </p>
      </div>
    </div>
  );
};

export default SampleTexts;
