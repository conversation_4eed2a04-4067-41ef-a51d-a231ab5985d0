import React from 'react';

const SampleTexts = ({ onSelectText }) => {
  const sampleTexts = [
    {
      category: "Nepali",
      texts: [
        "नमस्ते, म एक कृत्रिम बुद्धिमत्ता हुँ जसले नेपाली भाषामा बोल्न सक्छु।",
        "आज मौसम धेरै राम्रो छ। हामी बाहिर घुम्न जाऔं।",
        "नेपाल एक सुन्दर देश हो जहाँ हिमाल, पहाड र तराई छन्।",
        "शिक्षा मानव जीवनको सबैभन्दा महत्वपूर्ण भाग हो।"
      ]
    },
    {
      category: "Technical",
      texts: [
        "यो एक न्यूरल नेटवर्क आधारित टेक्स्ट टु स्पीच प्रणाली हो।",
        "कृत्रिम बुद्धिमत्ताले भविष्यमा धेरै क्षेत्रमा क्रान्ति ल्याउनेछ।",
        "डिजिटल प्रविधिले हाम्रो जीवनलाई सजिलो बनाएको छ।"
      ]
    }
  ];

  return (
    <div className="glass-card p-6 w-full">
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-8 h-8 bg-gradient-to-br from-amber-500 to-orange-500 rounded-lg flex items-center justify-center">
          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h2 className="text-xl font-bold text-gray-800">Sample Texts</h2>
      </div>

      <div className="space-y-4">
        {sampleTexts.map((category, categoryIndex) => (
          <div key={categoryIndex} className="space-y-3">
            <h3 className="text-sm font-medium text-gray-700 border-b border-white/20 pb-1">
              {category.category}
            </h3>
            <div className="space-y-3">
              {category.texts.map((text, textIndex) => (
                <div
                  key={textIndex}
                  className="glass-card p-4 cursor-pointer group hover:scale-105 transition-all duration-300 hover:shadow-lg"
                  onClick={() => onSelectText(text)}
                >
                  <div className="flex items-start justify-between">
                    <p className="flex-1 text-sm text-gray-700 group-hover:text-gray-900 leading-relaxed">
                      {text}
                    </p>
                    <div className="ml-3 flex-shrink-0">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 p-3 bg-white/10 rounded-lg border border-white/20">
        <p className="text-xs text-gray-600 text-center">
          Click on any sample text to use it for speech synthesis
        </p>
      </div>
    </div>
  );
};

export default SampleTexts;
