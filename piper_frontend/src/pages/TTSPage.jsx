import React, { useState, useEffect } from 'react';
import Header from '../components/Header';
import ModelSelector from '../components/ModelSelector';
import TextInput from '../components/TextInput';
import AudioPlayer from '../components/AudioPlayer';
import AudioSettings from '../components/AudioSettings';
import SampleTexts from '../components/SampleTexts';
import apiService from '../services/apiService';

const TTSPage = () => {
  const [apiStatus, setApiStatus] = useState('checking');
  const [models, setModels] = useState([]);
  const [currentModel, setCurrentModel] = useState(null);
  const [speakers, setSpeakers] = useState([]);
  const [voiceInfo, setVoiceInfo] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [audioUrl, setAudioUrl] = useState(null);
  
  // TTS Settings
  const [text, setText] = useState('');
  const [speakerId, setSpeakerId] = useState(0);
  const [speechRate, setSpeechRate] = useState(1.0);
  const [noiseScale, setNoiseScale] = useState(0.667);
  const [noiseW, setNoiseW] = useState(0.8);
  const [sentenceSilence, setSentenceSilence] = useState(0.2);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      const status = await apiService.checkApiStatus();
      setApiStatus(status);
      
      if (status === 'ready') {
        const [modelsData, voiceData, speakersData] = await Promise.all([
          apiService.loadAvailableModels(),
          apiService.loadVoiceInfo(),
          apiService.loadSpeakers()
        ]);
        
        setModels(modelsData);
        setVoiceInfo(voiceData);
        setSpeakers(speakersData);
        
        if (modelsData.length > 0) {
          setCurrentModel(modelsData[0]);
        }
      }
    } catch (error) {
      console.error('Failed to initialize app:', error);
      setApiStatus('error');
    }
  };

  const handleModelSwitch = async (model) => {
    try {
      setIsLoading(true);
      await apiService.switchModel(model.id);
      setCurrentModel(model);
      
      // Reload speakers for new model
      const speakersData = await apiService.loadSpeakers();
      setSpeakers(speakersData);
      setSpeakerId(0);
    } catch (error) {
      console.error('Failed to switch model:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSynthesize = async () => {
    if (!text.trim()) return;
    
    try {
      setIsLoading(true);
      const audioUrl = await apiService.synthesizeSpeech({
        text,
        speakerId,
        speechRate,
        noiseScale,
        noiseW,
        sentenceSilence
      });
      setAudioUrl(audioUrl);
    } catch (error) {
      console.error('Synthesis failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSampleTextSelect = (sampleText) => {
    setText(sampleText);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden">
      {/* Keep Same Animated Background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="particles-container">
          {[...Array(80)].map((_, i) => (
            <div
              key={i}
              className="particle"
              style={{
                animationDelay: `${Math.random() * 20}s`,
                top: `${20 + Math.random() * 60}%`,
                animationDuration: `${15 + Math.random() * 10}s`
              }}
            />
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen">
        <Header apiStatus={apiStatus} />

        <div className="container mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 max-w-7xl mx-auto">
            {/* Left Column - Model Selection */}
            <div className="lg:col-span-3">
              <ModelSelector
                models={models}
                currentModel={currentModel}
                onModelSwitch={handleModelSwitch}
                isLoading={isLoading}
                voiceInfo={voiceInfo}
              />
            </div>

            {/* Middle Column - Text Input & Audio Output */}
            <div className="lg:col-span-6 space-y-6">
              <TextInput
                text={text}
                setText={setText}
                onSynthesize={handleSynthesize}
                isLoading={isLoading}
                disabled={apiStatus !== 'ready'}
              />

              {audioUrl && (
                <AudioPlayer audioUrl={audioUrl} />
              )}
            </div>

            {/* Right Column - Settings & Sample Texts */}
            <div className="lg:col-span-3">
              <div className="space-y-6">
                <AudioSettings
                  speakers={speakers}
                  speakerId={speakerId}
                  setSpeakerId={setSpeakerId}
                  speechRate={speechRate}
                  setSpeechRate={setSpeechRate}
                  noiseScale={noiseScale}
                  setNoiseScale={setNoiseScale}
                  noiseW={noiseW}
                  setNoiseW={setNoiseW}
                  sentenceSilence={sentenceSilence}
                  setSentenceSilence={setSentenceSilence}
                />

                <SampleTexts onSelectText={handleSampleTextSelect} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TTSPage;
