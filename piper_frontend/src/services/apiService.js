const API_BASE = 'http://localhost:8000';

class ApiService {
  constructor() {
    this.baseUrl = API_BASE;
  }

  async checkApiStatus() {
    try {
      const response = await fetch(`${this.baseUrl}/health`);
      return response.ok ? 'ready' : 'error';
    } catch (error) {
      console.error('API status check failed:', error);
      return 'error';
    }
  }

  async loadAvailableModels() {
    try {
      const response = await fetch(`${this.baseUrl}/models`);
      if (response.ok) {
        const data = await response.json();
        return data.models || [];
      }
      return [];
    } catch (error) {
      console.error('Failed to load models:', error);
      return [];
    }
  }

  async loadVoiceInfo() {
    try {
      const response = await fetch(`${this.baseUrl}/voice/info`);
      if (response.ok) {
        const data = await response.json();
        return data;
      }
      return null;
    } catch (error) {
      console.error('Failed to load voice info:', error);
      return null;
    }
  }

  async loadSpeakers() {
    try {
      const response = await fetch(`${this.baseUrl}/speakers`);
      if (response.ok) {
        const data = await response.json();
        return data.speakers || [];
      }
      return [];
    } catch (error) {
      console.error('Failed to load speakers:', error);
      return [];
    }
  }

  async switchModel(modelId) {
    try {
      const response = await fetch(`${this.baseUrl}/models/${modelId}/load`, {
        method: 'POST'
      });
      if (response.ok) {
        const data = await response.json();
        return data;
      }
      throw new Error('Failed to switch model');
    } catch (error) {
      console.error('Model switch failed:', error);
      throw error;
    }
  }

  async synthesizeSpeech(params) {
    try {
      const urlParams = new URLSearchParams({
        text: params.text,
        speaker_id: params.speakerId,
        speech_rate: params.speechRate,
        noise_scale: params.noiseScale,
        noise_w: params.noiseW,
        sentence_silence: params.sentenceSilence
      });

      const response = await fetch(`${this.baseUrl}/synthesize?${urlParams}`);
      
      if (response.ok) {
        const audioBlob = await response.blob();
        return URL.createObjectURL(audioBlob);
      }
      throw new Error('Failed to synthesize speech');
    } catch (error) {
      console.error('Synthesis error:', error);
      throw error;
    }
  }
}

export default new ApiService();
