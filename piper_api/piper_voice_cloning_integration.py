#!/usr/bin/env python3
"""
Integration of Piper voice cloning with the existing piper_api.
This extends the current voice manager to support native voice cloning.
"""

import torch
import torchaudio
import numpy as np
from pathlib import Path
from typing import Optional, Dict, Any, Union
import logging
import tempfile
import io

try:
    from .voice import PiperVoice
except ImportError:
    from voice import PiperVoice

# Import voice cloning components
import sys
finetune_path = Path("../finetune")
if finetune_path.exists():
    sys.path.insert(0, str(finetune_path))

try:
    from piper_voice_cloning.vits_voice_cloning import VITSVoiceCloning
    from piper_voice_cloning.speaker_encoder import PiperSpeakerEncoder
    VOICE_CLONING_AVAILABLE = True
except ImportError:
    VOICE_CLONING_AVAILABLE = False
    VITSVoiceCloning = None
    PiperSpeakerEncoder = None

logger = logging.getLogger(__name__)

class PiperVoiceCloningVoice(PiperVoice):
    """Extended Piper voice with voice cloning capabilities."""
    
    def __init__(
        self,
        model_path: str,
        config_path: str,
        voice_cloning_model_path: Optional[str] = None,
        use_cuda: bool = True
    ):
        # Initialize base Piper voice
        super().__init__(model_path, config_path, use_cuda)
        
        self.voice_cloning_enabled = False
        self.voice_cloning_model = None
        
        # Load voice cloning model if available
        if voice_cloning_model_path and VOICE_CLONING_AVAILABLE:
            self.load_voice_cloning_model(voice_cloning_model_path)
    
    def load_voice_cloning_model(self, model_path: str):
        """Load voice cloning model."""
        try:
            logger.info(f"Loading voice cloning model from {model_path}")
            
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # Extract config
            config = checkpoint.get('config', {})
            speaker_embedding_dim = checkpoint.get('speaker_embedding_dim', 256)
            
            # Create voice cloning model
            vits_config = self._extract_vits_config_from_checkpoint(checkpoint)
            
            self.voice_cloning_model = VITSVoiceCloning(
                vits_config=vits_config,
                speaker_embedding_dim=speaker_embedding_dim,
                enable_voice_cloning=True
            )
            
            # Load state dict
            self.voice_cloning_model.load_state_dict(checkpoint['model_state_dict'])
            self.voice_cloning_model.to(self.device)
            self.voice_cloning_model.eval()
            
            self.voice_cloning_enabled = True
            logger.info("Voice cloning model loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load voice cloning model: {e}")
            self.voice_cloning_enabled = False
    
    def _extract_vits_config_from_checkpoint(self, checkpoint: Dict[str, Any]) -> Dict[str, Any]:
        """Extract VITS config from checkpoint."""
        # This would extract the actual config from your trained model
        # For now, return a default config
        return {
            'n_vocab': 256,
            'spec_channels': 513,
            'segment_size': 32,
            'inter_channels': 192,
            'hidden_channels': 192,
            'filter_channels': 768,
            'n_heads': 2,
            'n_layers': 6,
            'kernel_size': 3,
            'p_dropout': 0.1,
            'resblock': '1',
            'resblock_kernel_sizes': [3, 7, 11],
            'resblock_dilation_sizes': [[1, 3, 5], [1, 3, 5], [1, 3, 5]],
            'upsample_rates': [8, 8, 2, 2],
            'upsample_initial_channel': 512,
            'upsample_kernel_sizes': [16, 16, 4, 4],
            'n_speakers': 0,
            'gin_channels': 0,
        }
    
    def synthesize_with_voice_cloning(
        self,
        text: str,
        reference_audio: Union[bytes, torch.Tensor, str],
        speaker_id: Optional[int] = None,
        timbre_strength: float = 1.0,
        speed: float = 1.0,
        **kwargs
    ) -> bytes:
        """
        Synthesize speech with voice cloning.
        
        Args:
            text: Text to synthesize
            reference_audio: Reference audio (bytes, tensor, or file path)
            speaker_id: Original speaker ID (for mixing)
            timbre_strength: Strength of voice cloning (0.0 to 1.0)
            speed: Speech speed multiplier
        
        Returns:
            Audio bytes (WAV format)
        """
        if not self.voice_cloning_enabled:
            raise ValueError("Voice cloning is not enabled")
        
        # Process reference audio
        reference_tensor = self._process_reference_audio(reference_audio)
        
        # Convert text to phonemes using existing Piper functionality
        phoneme_ids, phonemes = self._text_to_phonemes(text)
        
        # Convert to tensors
        x = torch.tensor(phoneme_ids, dtype=torch.long).unsqueeze(0).to(self.device)
        x_lengths = torch.tensor([len(phoneme_ids)], dtype=torch.long).to(self.device)
        
        # Perform voice cloning inference
        with torch.no_grad():
            audio_output = self.voice_cloning_model.inference(
                x=x,
                x_lengths=x_lengths,
                reference_audio=reference_tensor,
                speaker_id=torch.tensor([speaker_id], dtype=torch.long).to(self.device) if speaker_id is not None else None,
                timbre_strength=timbre_strength,
                length_scale=1.0 / speed,
                **kwargs
            )
        
        # Convert to bytes
        return self._tensor_to_wav_bytes(audio_output)
    
    def transfer_timbre(
        self,
        source_audio: Union[bytes, torch.Tensor, str],
        target_voice: Union[bytes, torch.Tensor, str],
        strength: float = 0.8,
        preserve_prosody: bool = True
    ) -> bytes:
        """
        Transfer timbre from target voice to source audio.
        
        Args:
            source_audio: Source audio to modify
            target_voice: Target voice reference
            strength: Transfer strength
            preserve_prosody: Whether to preserve original prosody
        
        Returns:
            Audio bytes with transferred timbre
        """
        if not self.voice_cloning_enabled:
            raise ValueError("Voice cloning is not enabled")
        
        # Process audio inputs
        source_tensor = self._process_reference_audio(source_audio)
        target_tensor = self._process_reference_audio(target_voice)
        
        # Extract speaker embeddings
        with torch.no_grad():
            source_embedding = self.voice_cloning_model.extract_speaker_embedding(source_tensor)
            target_embedding = self.voice_cloning_model.extract_speaker_embedding(target_tensor)
            
            # Mix embeddings
            mixed_embedding = (
                source_embedding * (1.0 - strength) +
                target_embedding * strength
            )
        
        # For full timbre transfer, we'd need to re-synthesize the audio
        # This is a simplified version that returns the mixed embedding info
        logger.info("Timbre transfer completed (simplified version)")
        
        # Return original source audio for now
        # In full implementation, this would re-synthesize with new timbre
        return self._tensor_to_wav_bytes(source_tensor)
    
    def _process_reference_audio(
        self,
        audio_input: Union[bytes, torch.Tensor, str]
    ) -> torch.Tensor:
        """Process reference audio input to tensor."""
        if isinstance(audio_input, bytes):
            # Load from bytes
            with tempfile.NamedTemporaryFile(suffix=".wav") as temp_file:
                temp_file.write(audio_input)
                temp_file.flush()
                audio, sample_rate = torchaudio.load(temp_file.name)
        elif isinstance(audio_input, str):
            # Load from file path
            audio, sample_rate = torchaudio.load(audio_input)
        elif isinstance(audio_input, torch.Tensor):
            audio = audio_input
            sample_rate = 22050  # Assume default sample rate
        else:
            raise ValueError(f"Unsupported audio input type: {type(audio_input)}")
        
        # Convert to mono
        if audio.dim() > 1:
            audio = audio.mean(dim=0)
        
        # Resample if needed
        if sample_rate != 22050:
            resampler = torchaudio.transforms.Resample(sample_rate, 22050)
            audio = resampler(audio)
        
        return audio.to(self.device)
    
    def _tensor_to_wav_bytes(self, audio_tensor: torch.Tensor) -> bytes:
        """Convert audio tensor to WAV bytes."""
        # Ensure correct shape
        if audio_tensor.dim() == 1:
            audio_tensor = audio_tensor.unsqueeze(0)
        
        # Convert to CPU and numpy
        audio_np = audio_tensor.cpu().numpy()
        
        # Create WAV bytes
        with io.BytesIO() as wav_io:
            import soundfile as sf
            sf.write(wav_io, audio_np.T, 22050, format='WAV')
            return wav_io.getvalue()
    
    def get_voice_cloning_info(self) -> Dict[str, Any]:
        """Get information about voice cloning capabilities."""
        return {
            "voice_cloning_enabled": self.voice_cloning_enabled,
            "voice_cloning_available": VOICE_CLONING_AVAILABLE,
            "capabilities": [
                "voice_cloning",
                "timbre_transfer",
                "speaker_mixing"
            ] if self.voice_cloning_enabled else [],
            "speaker_embedding_dim": getattr(self.voice_cloning_model, 'speaker_embedding_dim', None) if self.voice_cloning_model else None
        }

class VoiceCloningModelManager:
    """Manager for voice cloning models."""
    
    def __init__(self, models_dir: str = "models"):
        self.models_dir = Path(models_dir)
        self.voice_cloning_models = {}
        self.load_voice_cloning_models()
    
    def load_voice_cloning_models(self):
        """Load available voice cloning models."""
        if not VOICE_CLONING_AVAILABLE:
            logger.warning("Voice cloning not available")
            return
        
        # Look for voice cloning models
        voice_cloning_dir = self.models_dir / "voice_cloning"
        if voice_cloning_dir.exists():
            for model_file in voice_cloning_dir.glob("*.pth"):
                model_id = model_file.stem
                config_file = model_file.with_suffix(".json")
                
                if config_file.exists():
                    try:
                        # Load basic info
                        self.voice_cloning_models[model_id] = {
                            "model_path": str(model_file),
                            "config_path": str(config_file),
                            "name": f"Voice Cloning {model_id}",
                            "type": "voice_cloning"
                        }
                        logger.info(f"Found voice cloning model: {model_id}")
                    except Exception as e:
                        logger.error(f"Failed to load voice cloning model {model_id}: {e}")
    
    def create_voice_cloning_voice(
        self,
        base_model_id: str,
        voice_cloning_model_id: Optional[str] = None,
        use_cuda: bool = True
    ) -> Optional[PiperVoiceCloningVoice]:
        """Create a voice with voice cloning capabilities."""
        # Get base model info
        base_model_dir = self.models_dir / base_model_id
        model_path = base_model_dir / "model.onnx"
        config_path = base_model_dir / "config.json"
        
        if not model_path.exists() or not config_path.exists():
            logger.error(f"Base model not found: {base_model_id}")
            return None
        
        # Get voice cloning model path
        voice_cloning_path = None
        if voice_cloning_model_id and voice_cloning_model_id in self.voice_cloning_models:
            voice_cloning_path = self.voice_cloning_models[voice_cloning_model_id]["model_path"]
        
        try:
            return PiperVoiceCloningVoice(
                model_path=str(model_path),
                config_path=str(config_path),
                voice_cloning_model_path=voice_cloning_path,
                use_cuda=use_cuda
            )
        except Exception as e:
            logger.error(f"Failed to create voice cloning voice: {e}")
            return None
    
    def get_available_voice_cloning_models(self) -> Dict[str, Dict[str, Any]]:
        """Get available voice cloning models."""
        return self.voice_cloning_models.copy()

# Global instance
voice_cloning_manager = VoiceCloningModelManager()
