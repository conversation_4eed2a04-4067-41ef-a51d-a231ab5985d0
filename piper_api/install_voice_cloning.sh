#!/bin/bash

# Voice Cloning Installation Script for Piper TTS API
# This script installs voice cloning capabilities

set -e

echo "🎯 Installing Voice Cloning Capabilities for Piper TTS API"
echo "=========================================================="

# Check if virtual environment is activated
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️  Warning: No virtual environment detected."
    echo "   It's recommended to run this in a virtual environment."
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Install basic dependencies
echo "📦 Installing basic audio processing dependencies..."
pip install soundfile librosa numpy scipy pydub aiofiles

# Install PyTorch if not already installed
echo "🔥 Checking PyTorch installation..."
if ! python -c "import torch" 2>/dev/null; then
    echo "📦 Installing PyTorch..."
    pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu
else
    echo "✅ PyTorch already installed"
fi

# Install XTTS v2 (Coqui TTS)
echo "🎤 Installing XTTS v2 (Coqui TTS)..."
pip install TTS

# Try to install F5-TTS
echo "🎵 Attempting to install F5-TTS..."
if pip install git+https://github.com/SWivid/F5-TTS.git; then
    echo "✅ F5-TTS installed successfully"
else
    echo "⚠️  F5-TTS installation failed (this is optional)"
fi

# Try to install OpenVoice v2
echo "🔊 Attempting to install OpenVoice v2..."
if pip install git+https://github.com/myshell-ai/OpenVoice.git; then
    echo "✅ OpenVoice v2 installed successfully"
else
    echo "⚠️  OpenVoice v2 installation failed (this is optional)"
fi

# Install additional audio utilities
echo "🛠️  Installing additional audio utilities..."
pip install resampy webrtcvad ffmpeg-python

echo ""
echo "🎉 Voice Cloning Installation Complete!"
echo ""
echo "📋 Installation Summary:"
echo "   ✅ Basic audio processing libraries"
echo "   ✅ XTTS v2 (Coqui TTS) - Multilingual voice cloning"
echo "   ⚠️  F5-TTS - High-quality voice cloning (optional)"
echo "   ⚠️  OpenVoice v2 - Timbre transfer (optional)"
echo ""
echo "🚀 Next Steps:"
echo "1. Start your Piper TTS API:"
echo "   cd piper_api && python start_api.py"
echo ""
echo "2. Test voice cloning endpoints:"
echo "   curl http://localhost:8000/voice-cloning/info"
echo ""
echo "3. Check available models:"
echo "   curl http://localhost:8000/voice-cloning/models"
echo ""
echo "📚 New API Endpoints Available:"
echo "   - POST /voice-cloning/clone - Clone voice from reference audio"
echo "   - POST /voice-cloning/timbre-transfer - Transfer timbre between audio"
echo "   - POST /voice-cloning/hybrid-synthesis - Combine Piper + voice cloning"
echo "   - GET /voice-cloning/info - Get voice cloning information"
echo "   - GET /voice-cloning/models - List available models"
echo ""
echo "💡 Note: Some models may require additional setup or model downloads"
echo "    Check the logs when starting the API for any missing dependencies"
