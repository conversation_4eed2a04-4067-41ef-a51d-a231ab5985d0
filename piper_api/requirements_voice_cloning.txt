# Voice Cloning Dependencies for Piper TTS API
# Install with: pip install -r requirements_voice_cloning.txt

# Core audio processing
soundfile>=0.12.1
librosa>=0.10.0
numpy>=1.21.0
scipy>=1.9.0

# F5-TTS (High-quality zero-shot voice cloning)
# Note: Install from GitHub as it may not be on PyPI yet
# git+https://github.com/SWivid/F5-TTS.git

# XTTS v2 (Coqui TTS - Multilingual voice cloning)
TTS>=0.22.0

# OpenVoice v2 (Instant voice cloning and timbre transfer)
# Note: Install from GitHub
# git+https://github.com/myshell-ai/OpenVoice.git

# Seed-VC (Real-time voice conversion)
# Note: Install from GitHub
# git+https://github.com/Plachtaa/seed-vc.git

# Additional audio utilities
pydub>=0.25.1
ffmpeg-python>=0.2.0

# Machine learning frameworks (if not already installed)
torch>=2.0.0
torchaudio>=2.0.0

# Optional: For advanced audio processing
resampy>=0.4.0
webrtcvad>=2.0.10

# For file handling and temporary files
aiofiles>=23.0.0

# Installation instructions:
# 1. Install basic dependencies:
#    pip install -r requirements_voice_cloning.txt
#
# 2. Install F5-TTS (if available):
#    pip install git+https://github.com/SWivid/F5-TTS.git
#
# 3. Install OpenVoice v2:
#    pip install git+https://github.com/myshell-ai/OpenVoice.git
#
# 4. Install Seed-VC (optional):
#    pip install git+https://github.com/Plachtaa/seed-vc.git
#
# Note: Some models may require additional setup or model downloads
