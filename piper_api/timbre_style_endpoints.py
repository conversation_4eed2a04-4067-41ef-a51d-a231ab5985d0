#!/usr/bin/env python3
"""
Timbre and Style Transfer API endpoints for Piper TTS.
"""

import logging
from typing import Optional, List, Dict, Any
from fastapi import Fast<PERSON>I, HTTPException, UploadFile, File, Form
from fastapi.responses import Response
from pydantic import BaseModel, Field

try:
    from .timbre_style_transfer import (
        TimbreStyleManager, TimbreTransferRequest, StyleTransferRequest,
        TimbreTransferModel, StyleTransferModel, EmotionType
    )
except ImportError:
    from timbre_style_transfer import (
        TimbreStyleManager, TimbreTransferRequest, StyleTransferRequest,
        TimbreTransferModel, StyleTransferModel, EmotionType
    )

logger = logging.getLogger(__name__)

# Pydantic models for API requests
class StyleTransferTextRequest(BaseModel):
    text: str = Field(..., description="Text to synthesize with style", min_length=1, max_length=2000)
    emotion: str = Field("neutral", description="Emotion to apply")
    emotion_strength: float = Field(1.0, description="Emotion strength", ge=0.0, le=2.0)
    speaking_rate: float = Field(1.0, description="Speaking rate multiplier", gt=0.1, le=3.0)
    pitch_shift: float = Field(0.0, description="Pitch shift in semitones", ge=-12.0, le=12.0)
    prosody_strength: float = Field(1.0, description="Prosody modification strength", ge=0.0, le=2.0)
    speaker_id: Optional[int] = Field(None, description="Speaker ID for multi-speaker models")

class TimbreTransferCapabilities(BaseModel):
    available_models: List[str]
    supports_speaker_id: bool
    supports_characteristics_extraction: bool

class StyleTransferCapabilities(BaseModel):
    available_models: List[str]
    available_emotions: List[str]
    supports_prosody_control: bool
    supports_pitch_control: bool
    supports_rate_control: bool

class TransferCapabilities(BaseModel):
    timbre_transfer: TimbreTransferCapabilities
    style_transfer: StyleTransferCapabilities

def add_timbre_style_endpoints(app: FastAPI, timbre_style_manager: TimbreStyleManager):
    """Add timbre and style transfer endpoints to the FastAPI app."""
    
    @app.get("/transfer/capabilities", response_model=TransferCapabilities, 
             summary="Get transfer capabilities")
    async def get_transfer_capabilities():
        """Get information about available timbre and style transfer capabilities."""
        try:
            capabilities = timbre_style_manager.get_capabilities()
            return TransferCapabilities(
                timbre_transfer=TimbreTransferCapabilities(**capabilities["timbre_transfer"]),
                style_transfer=StyleTransferCapabilities(**capabilities["style_transfer"])
            )
        except Exception as e:
            logger.error(f"Failed to get capabilities: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to get capabilities: {str(e)}")
    
    @app.get("/transfer/emotions", summary="Get available emotions")
    async def get_available_emotions():
        """Get list of available emotions for style transfer."""
        try:
            emotions = timbre_style_manager.get_available_emotions()
            return {
                "emotions": emotions,
                "default": "neutral",
                "description": "Available emotions for style transfer"
            }
        except Exception as e:
            logger.error(f"Failed to get emotions: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to get emotions: {str(e)}")
    
    @app.post("/transfer/timbre", summary="Transfer timbre between audio samples")
    async def transfer_timbre(
        source_audio: UploadFile = File(..., description="Source audio to modify"),
        target_voice: UploadFile = File(..., description="Target voice reference"),
        model_type: str = Form("piper_native", description="Timbre transfer model"),
        strength: float = Form(1.0, description="Transfer strength"),
        preserve_prosody: bool = Form(True, description="Preserve original prosody"),
        preserve_emotion: bool = Form(True, description="Preserve original emotion"),
        target_speaker_id: Optional[int] = Form(None, description="Target speaker ID")
    ):
        """
        Transfer timbre from target voice to source audio.
        
        - **source_audio**: Audio file whose timbre will be modified
        - **target_voice**: Reference audio containing the desired timbre
        - **model_type**: Model to use for timbre transfer (piper_native, openvoice_v2, seed_vc)
        - **strength**: How strongly to apply the timbre transfer (0.0-2.0)
        - **preserve_prosody**: Whether to preserve the original prosody/rhythm
        - **preserve_emotion**: Whether to preserve the original emotion
        - **target_speaker_id**: Use specific speaker ID instead of audio reference
        """
        try:
            # Validate model type
            try:
                model_enum = TimbreTransferModel(model_type)
            except ValueError:
                available_models = [model.value for model in TimbreTransferModel]
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid model_type. Available models: {available_models}"
                )
            
            # Validate file types
            if not source_audio.content_type.startswith(('audio/', 'video/')):
                raise HTTPException(
                    status_code=400,
                    detail="Source audio must be an audio file"
                )
            
            if not target_voice.content_type.startswith(('audio/', 'video/')):
                raise HTTPException(
                    status_code=400,
                    detail="Target voice must be an audio file"
                )
            
            # Read audio files
            source_audio_bytes = await source_audio.read()
            target_voice_bytes = await target_voice.read()
            
            if len(source_audio_bytes) == 0 or len(target_voice_bytes) == 0:
                raise HTTPException(
                    status_code=400,
                    detail="Audio files cannot be empty"
                )
            
            # Create timbre transfer request
            transfer_request = TimbreTransferRequest(
                source_audio=source_audio_bytes,
                target_voice=target_voice_bytes,
                model_type=model_enum,
                strength=strength,
                preserve_prosody=preserve_prosody,
                preserve_emotion=preserve_emotion,
                target_speaker_id=target_speaker_id
            )
            
            # Perform timbre transfer
            transferred_audio = timbre_style_manager.timbre_transfer.transfer_timbre(transfer_request)
            
            return Response(
                content=transferred_audio,
                media_type="audio/wav",
                headers={
                    "Content-Disposition": f"attachment; filename=timbre_transferred_{model_type}.wav"
                }
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Timbre transfer failed: {e}")
            raise HTTPException(status_code=500, detail=f"Timbre transfer failed: {str(e)}")
    
    @app.post("/transfer/style", summary="Synthesize text with style transfer")
    async def transfer_style(request: StyleTransferTextRequest):
        """
        Synthesize text with specified emotional style and prosody.
        
        - **text**: Text to synthesize (max 2000 characters)
        - **emotion**: Emotion to apply (neutral, happy, sad, angry, excited, calm, surprised, fearful)
        - **emotion_strength**: How strongly to apply the emotion (0.0-2.0)
        - **speaking_rate**: Speech rate multiplier (0.1-3.0)
        - **pitch_shift**: Pitch shift in semitones (-12.0 to 12.0)
        - **prosody_strength**: Prosody modification strength (0.0-2.0)
        - **speaker_id**: Speaker ID for multi-speaker models
        """
        try:
            # Validate emotion
            try:
                emotion_enum = EmotionType(request.emotion)
            except ValueError:
                available_emotions = [emotion.value for emotion in EmotionType]
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid emotion. Available emotions: {available_emotions}"
                )
            
            # Create style transfer request
            style_request = StyleTransferRequest(
                text=request.text,
                model_type=StyleTransferModel.EMOTION_CONTROL,
                emotion=emotion_enum,
                emotion_strength=request.emotion_strength,
                speaking_rate=request.speaking_rate,
                pitch_shift=request.pitch_shift,
                prosody_strength=request.prosody_strength,
                speaker_id=request.speaker_id
            )
            
            # Perform style transfer
            styled_audio = timbre_style_manager.style_transfer.apply_style_transfer(style_request)
            
            return Response(
                content=styled_audio,
                media_type="audio/wav",
                headers={
                    "Content-Disposition": f"attachment; filename=style_transfer_{request.emotion}.wav"
                }
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Style transfer failed: {e}")
            raise HTTPException(status_code=500, detail=f"Style transfer failed: {str(e)}")
    
    @app.post("/transfer/style-simple", summary="Simple style transfer via form")
    async def transfer_style_simple(
        text: str = Form(..., description="Text to synthesize"),
        emotion: str = Form("neutral", description="Emotion to apply"),
        emotion_strength: float = Form(1.0, description="Emotion strength"),
        speaking_rate: float = Form(1.0, description="Speaking rate"),
        pitch_shift: float = Form(0.0, description="Pitch shift in semitones"),
        speaker_id: Optional[int] = Form(None, description="Speaker ID")
    ):
        """
        Simple form-based style transfer endpoint.
        """
        request = StyleTransferTextRequest(
            text=text,
            emotion=emotion,
            emotion_strength=emotion_strength,
            speaking_rate=speaking_rate,
            pitch_shift=pitch_shift,
            prosody_strength=1.0,
            speaker_id=speaker_id
        )
        return await transfer_style(request)
    
    @app.post("/transfer/batch-style", summary="Batch style transfer for multiple texts")
    async def batch_style_transfer(
        texts: List[str] = Form(..., description="List of texts to synthesize"),
        emotion: str = Form("neutral", description="Emotion to apply to all texts"),
        emotion_strength: float = Form(1.0, description="Emotion strength"),
        speaking_rate: float = Form(1.0, description="Speaking rate"),
        speaker_id: Optional[int] = Form(None, description="Speaker ID")
    ):
        """
        Batch style transfer for multiple texts with the same style.
        Returns a ZIP file containing all generated audio files.
        """
        try:
            if len(texts) > 10:
                raise HTTPException(
                    status_code=400,
                    detail="Maximum 10 texts allowed for batch processing"
                )
            
            # Validate emotion
            try:
                emotion_enum = EmotionType(emotion)
            except ValueError:
                available_emotions = [emotion.value for emotion in EmotionType]
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid emotion. Available emotions: {available_emotions}"
                )
            
            import zipfile
            import io
            
            # Create ZIP file in memory
            zip_buffer = io.BytesIO()
            
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for i, text in enumerate(texts):
                    # Create style transfer request
                    style_request = StyleTransferRequest(
                        text=text,
                        model_type=StyleTransferModel.EMOTION_CONTROL,
                        emotion=emotion_enum,
                        emotion_strength=emotion_strength,
                        speaking_rate=speaking_rate,
                        pitch_shift=0.0,
                        prosody_strength=1.0,
                        speaker_id=speaker_id
                    )
                    
                    # Perform style transfer
                    styled_audio = timbre_style_manager.style_transfer.apply_style_transfer(style_request)
                    
                    # Add to ZIP
                    filename = f"styled_audio_{i+1:02d}_{emotion}.wav"
                    zip_file.writestr(filename, styled_audio)
            
            zip_buffer.seek(0)
            
            return Response(
                content=zip_buffer.getvalue(),
                media_type="application/zip",
                headers={
                    "Content-Disposition": f"attachment; filename=batch_style_transfer_{emotion}.zip"
                }
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Batch style transfer failed: {e}")
            raise HTTPException(status_code=500, detail=f"Batch style transfer failed: {str(e)}")
